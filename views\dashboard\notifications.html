{% extends 'layout.html' %}

{% block head %}
<title>Notifications - LHDN e-Invoice Portal</title>
<link href="/assets/css/components/tooltip.css" rel="stylesheet">
<link href="/assets/css/pages/notifications.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid px-3 px-md-4 px-lg-5">
  <!-- Header -->
  <div class="profile-welcome-card">
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <div class="welcome-icon">
          <i class="fas fa-bell"></i>
        </div>
        <div class="welcome-content">
          <h4 class="mb-1">Notifications Center</h4>
          <p class="mb-0">Stay updated with system notifications and LHDN alerts</p>
        </div>
      </div>
      <div class="notification-actions">
        <button class="btn btn-outline-primary btn-sm me-2" id="syncLHDNBtn">
          <i class="fas fa-sync-alt"></i> Sync LHDN
        </button>
        <button class="btn btn-outline-secondary btn-sm" id="markAllReadBtn">
          <i class="fas fa-check-double"></i> Mark All Read
        </button>
      </div>
    </div>
  </div>

  <!-- Notification Stats -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card notification-stat-card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stat-icon bg-primary">
              <i class="fas fa-bell"></i>
            </div>
            <div class="ms-3">
              <h5 class="mb-0" id="totalNotifications">0</h5>
              <small class="text-muted">Total Notifications</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card notification-stat-card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stat-icon bg-warning">
              <i class="fas fa-envelope"></i>
            </div>
            <div class="ms-3">
              <h5 class="mb-0" id="unreadNotifications">0</h5>
              <small class="text-muted">Unread</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card notification-stat-card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stat-icon bg-info">
              <i class="fas fa-globe"></i>
            </div>
            <div class="ms-3">
              <h5 class="mb-0" id="lhdnNotifications">0</h5>
              <small class="text-muted">LHDN Alerts</small>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card notification-stat-card">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="stat-icon bg-success">
              <i class="fas fa-cogs"></i>
            </div>
            <div class="ms-3">
              <h5 class="mb-0" id="systemNotifications">0</h5>
              <small class="text-muted">System</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Notification Filters -->
  <div class="card mb-4">
    <div class="card-body">
      <div class="row align-items-center">
        <div class="col-md-8">
          <div class="notification-filters">
            <button class="filter-btn active" data-filter="all">
              <i class="fas fa-list"></i> All
            </button>
            <button class="filter-btn" data-filter="unread">
              <i class="fas fa-envelope"></i> Unread
            </button>
            <button class="filter-btn" data-filter="system">
              <i class="fas fa-cogs"></i> System
            </button>
            <button class="filter-btn" data-filter="lhdn">
              <i class="fas fa-globe"></i> LHDN
            </button>
            <button class="filter-btn" data-filter="announcement">
              <i class="fas fa-bullhorn"></i> Announcements
            </button>
          </div>
        </div>
        <div class="col-md-4">
          <div class="d-flex align-items-center">
            <label class="form-label me-2 mb-0">Show:</label>
            <select class="form-select form-select-sm" id="notificationLimit">
              <option value="20">20 items</option>
              <option value="50" selected>50 items</option>
              <option value="100">100 items</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Notifications List -->
  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">
        <i class="fas fa-bell me-2"></i>
        Notifications
        <span class="badge bg-primary ms-2" id="notificationCount">0</span>
      </h5>
    </div>
    <div class="card-body p-0">
      <!-- Loading State -->
      <div id="notificationsLoading" class="text-center p-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">Loading notifications...</p>
      </div>

      <!-- Notifications Container -->
      <div id="notificationsContainer" class="notifications-list" style="display: none;">
        <!-- Notifications will be loaded here -->
      </div>

      <!-- Empty State -->
      <div id="notificationsEmpty" class="text-center p-5" style="display: none;">
        <i class="fas fa-bell-slash text-muted" style="font-size: 3rem;"></i>
        <h5 class="mt-3 text-muted">No notifications found</h5>
        <p class="text-muted">You're all caught up! No new notifications at this time.</p>
      </div>

      <!-- Error State -->
      <div id="notificationsError" class="text-center p-5" style="display: none;">
        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
        <h5 class="mt-3 text-warning">Failed to load notifications</h5>
        <p class="text-muted">There was an error loading your notifications. Please try again.</p>
        <button class="btn btn-primary" onclick="loadNotifications()">
          <i class="fas fa-refresh"></i> Retry
        </button>
      </div>
    </div>
  </div>

  <!-- Load More Button -->
  <div class="text-center mt-4" id="loadMoreContainer" style="display: none;">
    <button class="btn btn-outline-primary" id="loadMoreBtn">
      <i class="fas fa-chevron-down"></i> Load More Notifications
    </button>
  </div>
</div>

<!-- Notification Detail Modal -->
<div class="modal fade" id="notificationModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="notificationModalTitle">Notification Details</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="notificationModalBody">
        <!-- Notification details will be loaded here -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="markAsReadBtn" style="display: none;">
          Mark as Read
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/assets/js/pages/notifications.js"></script>
{% endblock %}
