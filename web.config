<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <!-- Redirect HTTP to HTTPS -->
    <rewrite>
      <rules>
        <rule name="Redirect to HTTPS" stopProcessing="true">
          <match url="(.*)" />
          <conditions>
            <add input="{HTTPS}" pattern="off" ignoreCase="true" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}{REQUEST_URI}" redirectType="Permanent" />
        </rule>
        <rule name="ReverseProxyToNode" stopProcessing="true">
          <match url="(.*)" />
          <action type="Rewrite" url="http://127.0.0.1:3000/{R:1}" />
          <serverVariables>
            <set name="HTTP_X_FORWARDED_FOR" value="{REMOTE_ADDR}" />
            <set name="HTTP_HOST" value="{HTTP_HOST}" />
          </serverVariables>
        </rule>
      </rules>
    </rewrite>
    

    <!-- Enable ARR proxy -->
    <proxy enabled="true" preserveHostHeader="true" />

    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="2147483648" />
      </requestFiltering>
    </security>

    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By"/>
        <add name="X-Frame-Options" value="SAMEORIGIN" />
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
        <add name="Access-Control-Allow-Origin" value="*" />
        <add name="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE, OPTIONS" />
        <add name="Access-Control-Allow-Headers" value="Content-Type, Authorization, X-Requested-With" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
</configuration>


<!-- <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="iisnode" path="server.js" verb="*" modules="iisnode" />
    </handlers>
    
    <iisnode
      nodeProcessCommandLine="&quot;%programfiles%\nodejs\node.exe&quot;"
      debuggingEnabled="false"
      loggingEnabled="true"
      devErrorsEnabled="false"
      maxNamedPipeConnectionRetry="50"
      namedPipeConnectionRetryDelay="500"
      maxNamedPipeConnectionPoolSize="256"
      maxNamedPipePooledConnectionAge="60000"
      initialRequestBufferSize="4096"
      maxRequestBufferSize="32768"
      watchedFiles="*.js;iisnode.yml"
      uncFileChangesPollingInterval="10000"
      gracefulShutdownTimeout="30000"
      logDirectory="iisnode"
      debuggerPortRange="5058-5558"
      maxLogFileSizeInKB="64"
      maxTotalLogFileSizeInKB="512"
      maxLogFiles="10"
      />

    <rewrite>
      <rules>
        <rule name="AuthPages" stopProcessing="true">
          <match url="^(auth/.*|api/v1/auth/.*|assets/.*|favicon\.ico)$" />
          <action type="Rewrite" url="server.js" />
        </rule>

        <rule name="StaticContent" stopProcessing="true">
          <match url="^(assets|public|uploads|temp|images)\/.*" />
          <action type="Rewrite" url="{REQUEST_URI}"/>
        </rule>

        <rule name="DynamicContent">
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true"/>
          </conditions>
          <action type="Rewrite" url="server.js"/>
        </rule>
      </rules>
    </rewrite>

    <security>
      <requestFiltering>
        <hiddenSegments>
          <remove segment="bin"/>
        </hiddenSegments>
        <requestLimits maxAllowedContentLength="2147483648" />
      </requestFiltering>
    </security>

    <httpErrors existingResponse="PassThrough" />
    
    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By"/>
        <add name="Cache-Control" value="no-cache, no-store, must-revalidate" />
        <add name="Pragma" value="no-cache" />
        <add name="Expires" value="-1" />
        <add name="Access-Control-Allow-Origin" value="*" />
        <add name="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE, OPTIONS" />
        <add name="Access-Control-Allow-Headers" value="Content-Type, Authorization" />
        <add name="Access-Control-Max-Age" value="86400" />
      </customHeaders>
    </httpProtocol>
    
    <serverRuntime uploadReadAheadSize="2147483648" />
    
    <webSocket enabled="false" />
  </system.webServer>

  <system.web>
    <customErrors mode="On"/>
    <sessionState mode="InProc" timeout="20" />
    <httpRuntime executionTimeout="120" maxRequestLength="10240" />
  </system.web>

  <location path="" overrideMode="Allow">
    <system.webServer>
      <security>
        <authentication>
          <anonymousAuthentication enabled="true" />
          <windowsAuthentication enabled="false" />
        </authentication>
      </security>
    </system.webServer>
  </location>
</configuration> -->