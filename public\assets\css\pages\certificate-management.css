/* Certificate Management Styles */

/* Certificate Upload Section */
.certificate-upload-section {
    background: #fff;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
}

.certificate-upload-section h5 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.certificate-upload-section h5 i {
    color: #3498db;
}

/* Upload Area */
.certificate-upload-area {
    border: 2px dashed #e0e6ed;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.certificate-upload-area:hover {
    border-color: #3498db;
    background: #f0f8ff;
}

.certificate-upload-area.dragover {
    border-color: #2980b9;
    background: #e8f4fd;
}

.upload-icon {
    font-size: 3rem;
    color: #bdc3c7;
    margin-bottom: 1rem;
}

.upload-text {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.upload-subtext {
    color: #95a5a6;
    font-size: 0.9rem;
}

/* Certificate Info Display */
.certificate-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.certificate-info h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.certificate-info h6 i {
    color: #27ae60;
}

.cert-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.cert-detail:last-child {
    border-bottom: none;
}

.cert-label {
    font-weight: 500;
    color: #34495e;
}

.cert-value {
    color: #7f8c8d;
    font-family: 'Roboto Mono', monospace;
    font-size: 0.9rem;
}

/* Status Badges */
.cert-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.cert-status.valid {
    background: #d4edda;
    color: #155724;
}

.cert-status.expired {
    background: #f8d7da;
    color: #721c24;
}

.cert-status.future {
    background: #fff3cd;
    color: #856404;
}

/* Requirements Check */
.requirements-section {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

.requirement-item i {
    font-size: 1.1rem;
}

.requirement-item.met i {
    color: #27ae60;
}

.requirement-item.missing i {
    color: #e74c3c;
}

.requirement-text {
    flex: 1;
    color: #2c3e50;
}

/* Action Buttons */
.certificate-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.btn-certificate {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-certificate.primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

.btn-certificate.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.btn-certificate.success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    color: white;
}

.btn-certificate.success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.btn-certificate.danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

.btn-certificate.danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-certificate:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    z-index: 10;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .certificate-upload-section {
        padding: 1rem;
    }
    
    .certificate-actions {
        flex-direction: column;
    }
    
    .btn-certificate {
        width: 100%;
        text-align: center;
    }
    
    .cert-detail {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* Animation for success states */
.certificate-success {
    animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Error states */
.certificate-error {
    border-color: #e74c3c !important;
    background: #fdf2f2 !important;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-message i {
    font-size: 1rem;
}
