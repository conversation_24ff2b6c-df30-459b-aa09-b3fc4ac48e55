<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #ffffff;
            font-size: 11px;
            line-height: 1.3;
        }

        .container {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 10px 0;
            background-color: #ffffff;
            position: relative;
        }

        /* Company Info */
        .company-info {
            margin-bottom: 10px;
        }

        .company-info img {
            max-width: 150px;
            margin-bottom: 5px;
            height: auto;
        }

        .company-info p {
            margin: 1px 0;
        }

        .company-name {
            font-weight: bold;
        }

        .email-link {
            color: #000;
            text-decoration: underline;
        }

        /* Invoice Header */
        .invoice-header {
            text-align: right;
            margin-top: -80px;
            margin-bottom: 30px;
        }

        .invoice-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }

        .invoice-details {
            text-align: right;
            margin-bottom: 20px;
        }

        .invoice-details p {
            margin: 1px 0;
        }

        /* Main Content */
        .main-content {
            margin: 20px 0;
        }

        .info-row {
            margin: 1px 0;
        }

        .info-label {
            font-weight: bold;
        }

        /* Table Styles */
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 10px;
        }

        .details-table th,
        .details-table td {
            border: 1px solid #000;
            padding: 4px;
            text-align: left;
        }

        .details-table th {
            background-color: #000;
            color: #fff;
            font-weight: bold;
            font-size: 10px;
            white-space: nowrap;
        }

        .text-center { text-align: center; }
        .text-right { text-align: right; }

        /* Tax Summary Table */
        .tax-summary-table {
            width: auto;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 10px;
        }

        .tax-summary-table th,
        .tax-summary-table td {
            border: 1px solid #000;
            padding: 4px 8px;
            text-align: left;
            min-width: 80px;
        }

        .tax-summary-table th {
            background-color: #000;
            color: #fff;
            font-weight: bold;
        }

        /* Totals Section */
        .totals-table {
            width: 250px;
            margin-left: auto;
            border-collapse: collapse;
            font-size: 10px;
        }

        .totals-table tr td {
            padding: 4px 8px;
            border: 1px solid #000;
        }

        .totals-table tr:last-child {
            font-weight: bold;
        }

        .total-label {
            text-align: right;
            white-space: nowrap;
            font-weight: bold;
        }

        .total-amount {
            text-align: right;
            width: 100px;
            background-color: #f9f9f9;
        }

        .total-amount.final {
            background-color: #e9e9e9;
        }

        /* Footer */
        .footer {
            margin-top: 30px;
            border-top: 1px solid #000;
            padding-top: 10px;
            position: relative;
            min-height: 100px;
            font-size: 10px;
        }

        .signature-block {
            width: calc(100% - 120px);
        }

        .signature-block p {
            margin: 1px 0;
        }

        .signature-title {
            font-weight: bold;
        }

        .qr-code {
            position: absolute;
            right: 0;
            top: 10px;
            width: 100px;
            height: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Company Info -->
        <div class="company-info">
            {{if CompanyLogo}}
                <img src="{{:CompanyLogo}}" alt="Company Logo">
            {{/if}}
            <p class="company-name">{{:companyName}}</p>
            <p>{{:companyAddress}}</p>
            <p>{{:companyPhone}}</p>
            <p><a href="mailto:{{:companyEmail}}" class="email-link">{{:companyEmail}}</a></p>
        </div>

        <!-- Invoice Header -->
        <div class="invoice-header">
            <div class="invoice-title">Tax Invoice</div>
            <div class="invoice-details">
                <p><span class="info-label">e-Invoice No:</span> {{:internalId}}</p>
                <p><span class="info-label">e-Invoice type:</span> {{:InvoiceTypeName}}</p>
                <p><span class="info-label">e-Invoice version:</span> {{:InvoiceVersion}}</p>
                <p><span class="info-label">Unique Identifier No:</span> {{:UniqueIdentifier}}</p>
                <p><span class="info-label">Original Invoice Ref. No.:</span> {{:OriginalInvoiceRef}}</p>
                <p><span class="info-label">Issue Date:</span> {{:issueDate}} {{:issueTime}}</p>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="info-row"><span class="info-label">Supplier TIN:</span> {{:SupplierTIN}}</div>
            <div class="info-row"><span class="info-label">Supplier Registration Number:</span> {{:SupplierRegistrationNumber}}</div>
            <div class="info-row"><span class="info-label">Supplier SST ID:</span> {{:SupplierSSTID}}</div>
            <div class="info-row"><span class="info-label">Supplier MSIC code:</span> {{:SupplierMSICCode}}</div>
            <div class="info-row"><span class="info-label">Supplier business activity description:</span> {{:SupplierBusinessActivity}}</div>
            <div class="info-row"><span class="info-label"></span></div>
            <div class="info-row"><span class="info-label">Buyer TIN:</span> {{:BuyerTIN}}</div>
            <div class="info-row"><span class="info-label">Buyer Name:</span> {{:BuyerName}}</div>
            <div class="info-row"><span class="info-label">Buyer Registration No:</span> {{:BuyerRegistrationNumber}}</div>
            <div class="info-row"><span class="info-label">Buyer Address:</span> {{:BuyerAddress}}</div>
            <div class="info-row"><span class="info-label">Buyer Contact Number:</span> {{:BuyerPhone}}</div>
            <div class="info-row"><span class="info-label">Buyer Email:</span> {{:companyEmail}}</div>
        </div>

        <!-- Main Table -->
        <table class="details-table">
            <tr>
                <th>No.</th>
                <th>Classification</th>
                <th>Description</th>
                <th>Quantity</th>
                <th>Unit Price</th>
                <th>Amount</th>
                <th>Disc</th>
                <th>Tax Rate</th>
                <th>Tax Amount</th>
                <th>Total</th>
            </tr>
            {{for items}}
            <tr>
                <td class="text-center">{{:No}}</td>
                <td class="text-center">{{:Cls}}</td>
                <td>{{:Description}}</td>
                <td class="text-right">{{:Quantity}}</td>
                <td class="text-right">{{:documentCurrency}} {{:UnitPrice}}</td>
                <td class="text-right">{{:documentCurrency}} {{:QtyAmount}}</td>
                <td class="text-right">{{if Disc === '-'}}{{:Disc}}{{else}}{{:documentCurrency}} {{:Disc}}{{/if}}</td>
                <td class="text-center">{{:LineTaxPercent}}%</td>
                <td class="text-right">{{:documentCurrency}} {{:LineTaxAmount}}</td>
                <td class="text-right">{{:documentCurrency}} {{:Total}}</td>
            </tr>
            {{/for}}
        </table>

        <!-- Totals -->
        <table class="totals-table">
            <tr>
                <td class="total-label">Subtotal</td>
                <td class="total-amount">{{:documentCurrency}} {{:Subtotal}}</td>
            </tr>
            <tr>
                <td class="total-label">Total excluding tax</td>
                <td class="total-amount">{{:documentCurrency}} {{:TotalExcludingTax}}</td>
            </tr>
            <tr>
                <td class="total-label">Tax amount</td>
                <td class="total-amount">{{:documentCurrency}} {{:TotalTaxAmount}}</td>
            </tr>
            <tr>
                <td class="total-label">Total including tax</td>
                <td class="total-amount">{{if TotalIncludingTax == "0.00"}}{{:documentCurrency}}0.00{{else}}{{:documentCurrency}} {{:TotalIncludingTax}}{{/if}}</td>
            </tr>
            <tr>
                <td class="total-label">Total payable amount</td>
                <td class="total-amount final">{{:documentCurrency}} {{:TotalPayableAmount}}</td>
            </tr>
        </table>

        <!-- Tax Summary Table -->
        <table class="details-table" style="margin-top: 20px; margin-right: 30px;">
            <tr>
                <th>Total Product / Service Price</th>
                <th>Tax type</th>
                <th>Tax Rate</th>
                <th>Tax amount</th>
            </tr>
            <tr>
                <td class="text-right">{{:documentCurrency}} {{:TotalExcludingTax}}</td>
                <td>{{:TaxSchemeId}}</td>
                <td class="text-center">{{:TaxRate}}%</td>
                <td class="text-right">{{:documentCurrency}} {{:TotalTaxAmount}}</td>
            </tr>
        </table>

        <!-- Footer -->
        <div class="footer">
            <div class="signature-block">
                <p><span class="signature-title">Digital Signature:</span> {{:DigitalSignature}}</p>
                <p><span class="signature-title">Date and Time of Validation:</span> {{:validationDateTime}}</p>
                <p>This document is a visual presentation of the e-Invoice.</p>
            </div>
            {{if qrCode}}
                <img src="{{:qrCode}}" alt="QR Code" class="qr-code">
            {{/if}}
        </div>
    </div>
</body>
</html>