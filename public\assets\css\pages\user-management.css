/* User Management Container */
.user-management-container {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 80px);
    background: #f8fafc;
}

/* Content Layout */
.content-grid {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
}

/* Stats Card */
.stats-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stats-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.5rem;
  background: #f9fafb;
  margin-bottom: 1rem;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total {
  background: #3b82f6;
}

.stat-icon.admin {
  background: #8b5cf6;
}

.stat-icon.regular {
  background: #10b981;
}

.stat-details {
  flex: 1;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* User List Section */
.user-list-section {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.list-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.add-user-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.add-user-btn:hover {
  background: #2563eb;
}

/* User Table */
.user-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: white;
  border-radius: 0.5rem;
  overflow: hidden;
}

.user-table th {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  text-align: left;
  padding: 1rem;
  font-size: 0.875rem;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
}

.user-table td {
  padding: 1rem;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
  vertical-align: middle;
}

.user-table tr:hover {
  background-color: #f9fafb;
}

.user-table tr:last-child td {
  border-bottom: none;
}

/* Role Badge */
.role-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.role-badge.admin {
  background: #fee2e2;
  color: #dc2626;
}

.role-badge.regular {
  background: #ecfdf5;
  color: #059669;
}

/* User Actions */
.user-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.edit {
  background: #dbeafe;
  color: #2563eb;
}

.action-btn.edit:hover {
  background: #bfdbfe;
}

.action-btn.delete {
  background: #fee2e2;
  color: #dc2626;
}

.action-btn.delete:hover {
  background: #fecaca;
}

/* Modal Styles */
.user-modal .modal-content {
  border-radius: 1rem;
  border: none;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(0);
  transition: transform 0.3s ease-out;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
}

.user-modal.fade .modal-content {
  transform: translateY(-20px);
  opacity: 0;
}

.user-modal.show .modal-content {
  transform: translateY(0);
  opacity: 1;
}

.user-modal .modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  background: linear-gradient(to right, #f8fafc, #f1f5f9);
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}

.user-modal .modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #0f172a;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  letter-spacing: -0.025em;
}

.user-modal .modal-title i {
  color: #3b82f6;
  font-size: 1.35rem;
}

.user-modal .modal-body {
  padding: 2rem 1.5rem;
  background: white;
}

.user-modal .form-group {
  margin-bottom: 1.5rem;
}

.user-modal .form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.user-modal .form-label i {
  color: #64748b;
  font-size: 0.875rem;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.user-modal .form-label i:hover {
  opacity: 1;
}

.user-modal .form-control {
  width: 100%;
  padding: 0.625rem 0.875rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #0f172a;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  transition: all 0.2s;
}

.user-modal .form-control:hover {
  background-color: #f1f5f9;
}

.user-modal .form-control:focus {
  outline: none;
  border-color: #3b82f6;
  background-color: white;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.user-modal .checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
}

.user-modal .checkbox-group:hover {
  background: #f1f5f9;
}

.user-modal .checkbox-group input[type="checkbox"] {
  width: 1.125rem;
  height: 1.125rem;
  border-radius: 0.25rem;
  border: 2px solid #94a3b8;
  cursor: pointer;
  transition: all 0.2s;
}

.user-modal .checkbox-group input[type="checkbox"]:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.user-modal .checkbox-group input[type="checkbox"]:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.user-modal .modal-footer {
  padding: 1.25rem 1.5rem;
  border-top: 1px solid rgba(229, 231, 235, 0.5);
  background: linear-gradient(to right, #f8fafc, #f1f5f9);
  border-bottom-left-radius: 1rem;
  border-bottom-right-radius: 1rem;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.user-modal .modal-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.user-modal .modal-btn i {
  font-size: 1rem;
}

.user-modal .modal-btn.cancel {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.user-modal .modal-btn.cancel:hover {
  background: #e2e8f0;
  color: #334155;
  transform: translateY(-1px);
}

.user-modal .modal-btn.save {
  background: linear-gradient(to right, #3b82f6, #2563eb);
  color: white;
  border: none;
}

.user-modal .modal-btn.save:hover {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2);
}

.user-modal .text-danger {
  color: #dc2626;
}

/* Loading State */
.user-modal .modal-content.loading {
  position: relative;
}

.user-modal .modal-content.loading::after {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  border-radius: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.user-modal .loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 11;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(59, 130, 246, 0.1);
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-card {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .add-user-btn {
    width: 100%;
    justify-content: center;
  }
  
  .user-table {
    display: block;
    overflow-x: auto;
  }
}

/* Loading States */
.loading-overlay {
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
}

.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}