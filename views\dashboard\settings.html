{% extends 'layout.html' %}

{% block head %}
<title>Settings - LHDN e-Invoice Portal</title>
<link href="/assets/css/components/tooltip.css" rel="stylesheet">
<link href="/assets/css/pages/settings.css" rel="stylesheet">
<link href="/assets/css/pages/profile.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid px-3 px-md-4 px-lg-5">
  <!-- Header -->
  <div class="profile-welcome-card">
    <h2>
      <i class="fas fa-cog"></i>
      LHDN Portal Settings
    </h2>
    <p>Configure your LHDN e-Invoice middleware settings and integration preferences</p>
    <div class="alert alert-warning" role="alert" style="font-size: 0.75rem;">
      <i class="fas fa-exclamation-triangle fa-spin text-warning me-1"></i>
      This feature is in experimental stage and under active development
    </div>
  </div>

  <div class="settings-content">
    <!-- Left Sidebar - Settings Navigation -->
    <div class="settings-nav-card">
      <h6 class="settings-nav-title">
        <i class="fas fa-sliders"></i>
        Settings Menu
      </h6>
      
      <div class="settings-nav-items">
        <a href="#company" class="settings-nav-item active" data-section="company">
          <div class="settings-nav-icon">
            <i class="fas fa-building"></i>
          </div>
          <div class="settings-nav-details">
            <h4>Company Profile</h4>
            <p>Business and tax registration details</p>
          </div>
        </a>

        <a href="#api" class="settings-nav-item" data-section="api">
          <div class="settings-nav-icon">
            <i class="fas fa-plug"></i>
          </div>
          <div class="settings-nav-details">
            <h4>API Configuration</h4>
            <p>LHDN API credentials and endpoints</p>
          </div>
        </a>

        <a href="#invoice" class="settings-nav-item" data-section="invoice">
          <div class="settings-nav-icon">
            <i class="fas fa-file-invoice"></i>
          </div>
          <div class="settings-nav-details">
            <h4>Invoice Settings</h4>
            <p>Invoice format and requirements</p>
          </div>
        </a>


        <a href="#validation" class="settings-nav-item" data-section="validation">
          <div class="settings-nav-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="settings-nav-details">
            <h4>Validation Rules</h4>
            <p>LHDN compliance validation</p>
          </div>
        </a>

        <a href="#logs" class="settings-nav-item" data-section="logs">
          <div class="settings-nav-icon">
            <i class="fas fa-history"></i>
          </div>
          <div class="settings-nav-details">
            <h4>Logging & Monitoring</h4>
            <p>Audit trail and error tracking</p>
            
          </div>
        </a>



     
      </div>
    </div>

    <!-- Right Content - Settings Forms -->
    <div class="settings-form-section">
      <form id="settingsForm">
        <!-- Company Profile -->
        <div class="settings-form active" id="company">
          <h3 class="settings-form-title">
            <i class="fas fa-building"></i>
            Company Profile
          </h3>
          <div class="alert alert-warning" role="alert" style="font-size: 0.75rem;">
            <i class="fas fa-exclamation-triangle fa-spin text-warning me-1"></i>
            This feature is in experimental stage and under active development
          </div>
          <div class="alert alert-info" role="alert" style="font-size: 0.75rem;">
            <i class="fas fa-info-circle fa-spin text-info me-1 mb-2 "></i> Company profile information is managed in the Profile section. Changes to company details should be made there.
          </div>
          <div class="settings-form-content">
            <div class="form-group">
              <label>Company Name <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Enter your company's legally registered business name"></i></label>
              <input type="text" class="form-control" id="companyName" placeholder="Legal registered name" disabled>
            </div>

            <div class="form-group">
              <label>Business Registration Number (ROC) <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Your company's registration number issued by SSM (e.g., 123456-A)"></i></label>
              <input type="text" class="form-control" id="rocNumber" placeholder="e.g., 123456-A" disabled>
            </div>

            <div class="form-group">
              <label>Tax Registration Number <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Your company's tax registration number issued by LHDN"></i></label>
              <input type="text" class="form-control" id="taxNumber" placeholder="Tax registration number" disabled>
            </div>

            <div class="form-group">
              <label>SST Registration Number <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Your Sales and Service Tax registration number if applicable"></i></label>
              <input type="text" class="form-control" id="sstNumber" placeholder="SST registration number" disabled>
            </div>

            <div class="form-group">
              <label>Business Address <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Your company's official registered business address"></i></label>
              <textarea class="form-control" id="businessAddress" rows="3" placeholder="Registered business address" disabled></textarea>
            </div>

            <div class="form-group">
              <label>Contact Information <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Primary contact details for communications"></i></label>
              <input type="email" class="form-control" id="contactEmail" placeholder="Primary contact email" disabled>
              <input type="tel" class="form-control mt-2" id="contactPhone" placeholder="Contact phone number" disabled>
            </div>
          </div>
        </div>

        <!-- API Configuration -->
        <div class="settings-form" id="api">
          <h3 class="settings-form-title">
            <i class="fas fa-plug"></i>
            API Configuration
          </h3>
          <div class="alert alert-warning" role="alert" style="font-size: 0.75rem;">
            <i class="fas fa-exclamation-triangle fa-spin text-warning me-1"></i>
            This feature is in experimental stage and under active development
          </div>
          <div class="settings-form-content">
            <div class="form-group">
              <label>LHDN Environment <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Select the environment for API integration - use Sandbox for testing"></i></label>
              <select class="form-control" id="apiEnvironment">
                <option value="sandbox">Sandbox (Testing)</option>
                <option value="production">Production</option>
              </select>
            </div>

            <div class="form-group">
              <label>API Credentials <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Your LHDN API authentication credentials"></i></label>
              <input type="text" class="form-control" id="apiKey" placeholder="Client ID">
              <div class="input-group mt-2">
                <input type="password" class="form-control" id="apiSecret" placeholder="Client Secret">
                <button class="btn btn-secondary" onclick="toggleSecret('apiSecret')">Show</button>
              </div>
            </div>

            <div class="form-group">
              <label>API Endpoints <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Visit the LHDN SDK official website to obtain the correct API endpoint"></i></label>
              <input type="url" class="form-control" id="apiEndpoint" placeholder="Base API URL">
              <div class="form-check mt-2">
                <input type="checkbox" class="form-check-input" id="useCustomEndpoint">
                <label class="form-check-label">Use custom endpoint <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Enable to use a custom API endpoint instead of the default"></i></label>
              </div>
            </div>

            <div class="form-group">
              <label>Request Timeout (seconds) <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Maximum time to wait for API response (30-300 seconds)"></i></label>
              <input type="number" class="form-control" id="requestTimeout" min="30" max="300" value="60">
            </div>

            <div class="form-group">
              <label>SSL/TLS Configuration <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure secure connection settings"></i></label>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="verifySSL">
                <label class="form-check-label">Verify SSL certificate <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Enable SSL certificate verification for secure connections"></i></label>
              </div>
              <input type="file" class="form-control mt-2" id="sslCert" accept=".pem,.crt,.key">
            </div>
          </div>
        </div>

        <!-- Submission Rules -->
        <div class="settings-form" id="submission">
          <h3 class="settings-form-title">
            <i class="fas fa-paper-plane"></i>
            Submission Rules
          </h3>
          <div class="settings-form-content">
            <div class="form-group">
              <label>Submission Mode <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure how invoices are submitted to LHDN"></i></label>
              <select class="form-control" id="submissionMode">
                <option value="auto">Automatic (Submit immediately)</option>
                <option value="manual">Manual (Require approval)</option>
                <option value="batch">Batch (Submit in groups)</option>
              </select>
            </div>

            <div class="form-group">
              <label>Batch Settings <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure batch submission settings"></i></label>
              <input type="number" class="form-control" id="batchSize" placeholder="Number of invoices per batch">
              <select class="form-control mt-2" id="batchSchedule">
                <option value="hourly">Every Hour</option>
                <option value="daily">Once Daily</option>
                <option value="weekly">Once Weekly</option>
              </select>
              <input type="time" class="form-control mt-2" id="batchTime" placeholder="Scheduled time">
            </div>

            <div class="form-group">
              <label>Retry Settings <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure retry behavior for failed submissions"></i></label>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="enableRetry">
                <label class="form-check-label">Enable automatic retry <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Automatically retry failed submissions"></i></label>
              </div>
              <input type="number" class="form-control mt-2" id="maxRetries" placeholder="Maximum retry attempts">
              <input type="number" class="form-control mt-2" id="retryDelay" placeholder="Delay between retries (minutes)">
            </div>

            <div class="form-group">
              <label>Duplicate Handling <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure how duplicate submissions are handled"></i></label>
              <select class="form-control" id="duplicateAction">
                <option value="reject">Reject Duplicate</option>
                <option value="update">Update Existing</option>
                <option value="queue">Queue for Review</option>
              </select>
              <input type="number" class="form-control mt-2" id="duplicateCooldown" placeholder="Duplicate submission cooldown (minutes)">
            </div>

            <div class="form-group">
              <label>Submission Notifications <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure submission notification settings"></i></label>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="notifySuccess">
                <label class="form-check-label">Notify on successful submission <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Send notification for successful submissions"></i></label>
              </div>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="notifyFailure">
                <label class="form-check-label">Notify on submission failure <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Send notification for failed submissions"></i></label>
              </div>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="notifyRetry">
                <label class="form-check-label">Notify on retry attempts <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Send notification when retrying submissions"></i></label>
              </div>
            </div>
          </div>
        </div>

        <!-- Invoice Settings -->
        <div class="settings-form" id="invoice">
          <h3 class="settings-form-title">
            <i class="fas fa-file-invoice"></i>
            Invoice Settings
          </h3>
          <div class="alert alert-warning" role="alert" style="font-size: 0.75rem;">
            <i class="fas fa-exclamation-triangle fa-spin text-warning me-1"></i>
            This feature is in experimental stage and under active development
          </div>
          <div class="settings-form-content">
            <div class="form-group">
              <label>Invoice Format <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Select the format for invoice data submission"></i></label>
              <select class="form-control" id="invoiceFormat">
                <option value="json">JSON</option>
                <option value="xml">XML</option>
              </select>
            </div>

            <div class="form-group">
              <label>Currency Settings <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Set invoice currency and conversion preferences"></i></label>
              <select class="form-control" id="currency">
                <option value="MYR">Malaysian Ringgit (MYR)</option>
                <option value="USD">US Dollar (USD)</option>
              </select>
              <div class="form-check mt-2">
                <input type="checkbox" class="form-check-input" id="autoConvert">
                <label class="form-check-label">Auto-convert to MYR <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Automatically convert foreign currencies to MYR"></i></label>
              </div>
            </div>
          </div>
        </div>

        <!-- Digital Signature Settings (Coming Soon) -->
        <div class="settings-form" id="digital-signature">
          <div class="coming-soon-overlay">
            <div class="text-center p-5">
              <i class="fas fa-lock fa-3x mb-3" style="color: #9ca3af;"></i>
              <h4 class="mb-3" style="color: #4b5563;">Digital Signature</h4>
              <div class="badge bg-warning text-dark mb-3">Coming Soon</div>
              <p class="text-muted">This feature is currently under development</p>
            </div>
          </div>
        </div>

      
        <!-- Validation Rules -->
        <div class="settings-form" id="validation">
          <h3 class="settings-form-title">
            <i class="fas fa-check-circle"></i>
            Validation Rules
          </h3>
          <div class="alert alert-warning" role="alert" style="font-size: 0.75rem;">
            <i class="fas fa-exclamation-triangle fa-spin text-warning me-1"></i>
            This feature is in experimental stage and under active development
          </div>
          <div class="settings-form-content">
            <div class="form-group">
              <label>Required Fields Validation <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure validation for required fields"></i></label>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="validateInvoiceNo">
                <label class="form-check-label">Invoice number format <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Validate invoice number format"></i></label>
              </div>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="validateTaxId">
                <label class="form-check-label">Tax registration numbers <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Validate tax registration number format"></i></label>
              </div>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="validateDates">
                <label class="form-check-label">Invoice and tax dates <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Validate date formats and ranges"></i></label>
              </div>
            </div>

            <div class="form-group">
              <label>Amount Validation <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure amount validation rules"></i></label>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="validateTotals">
                <label class="form-check-label">Line item totals match invoice total <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Verify line item totals sum matches invoice total"></i></label>
              </div>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="validateTaxCalc">
                <label class="form-check-label">Tax calculations <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Verify tax calculations are correct"></i></label>
              </div>
            </div>

            <div class="form-group">
              <label>Business Rules <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure business validation rules"></i></label>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="checkDuplicates">
                <label class="form-check-label">Check for duplicate invoices <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Prevent duplicate invoice submissions"></i></label>
              </div>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="validateCurrency">
                <label class="form-check-label">Currency validation <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Validate currency codes and conversion rates"></i></label>
              </div>
            </div>

            <div class="form-group">
              <label>Error Handling <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure how validation errors are handled"></i></label>
              <select class="form-control" id="errorAction">
                <option value="reject">Reject invalid invoices</option>
                <option value="queue">Queue for review</option>
                <option value="auto-correct">Attempt auto-correction</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Logging & Monitoring -->
        <div class="settings-form" id="logs">
          <h3 class="settings-form-title">
            <i class="fas fa-history"></i>
            Logging & Monitoring 
          </h3>
          <div class="alert alert-warning" role="alert" style="font-size: 0.75rem;">
            <i class="fas fa-exclamation-triangle fa-spin text-warning me-1"></i>
            This feature is in experimental stage and under active development
          </div>
          <div class="settings-form-content">
            <div class="form-group">
              <label>Log Level <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Set the detail level for logging"></i></label>
              <select class="form-control" id="logLevel">
                <option value="error">Error only</option>
                <option value="warn">Warning and above</option>
                <option value="info">Info and above</option>
                <option value="debug">Debug (Verbose)</option>
              </select>
            </div>

            <div class="form-group">
              <label>Log Storage <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure log storage settings"></i></label>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="storeLocal">
                <label class="form-check-label">Store logs locally <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Keep logs on local storage"></i></label>
              </div>
              <input type="number" class="form-control mt-2" id="retentionDays" placeholder="Retention period (days)">
            </div>

            <div class="form-group">
              <label>Error Notifications <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure error notification settings"></i></label>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="notifyErrors">
                <label class="form-check-label">Send error notifications <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Enable email notifications for errors"></i></label>
              </div>
              <textarea class="form-control mt-2" id="notifyEmails" rows="2" placeholder="Notification emails (one per line)"></textarea>
            </div>

            <div class="form-group">
              <label>Monitoring <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure system monitoring settings"></i></label>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="monitorPerformance">
                <label class="form-check-label">Monitor API performance <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Track API response times and performance metrics"></i></label>
              </div>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="monitorQuota">
                <label class="form-check-label">Monitor API quota usage <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Track API usage against quota limits"></i></label>
              </div>
            </div>

            <div class="form-group">
              <label>Audit Trail <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Configure audit logging settings"></i></label>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="auditChanges">
                <label class="form-check-label">Log configuration changes <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Track all configuration changes"></i></label>
              </div>
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="auditAccess">
                <label class="form-check-label">Log access attempts <i class="fas fa-info-circle tooltip-icon" data-bs-toggle="tooltip" data-bs-placement="right" title="Track all system access attempts"></i></label>
              </div>
            </div>
          </div>
        </div>
        <!-- Form Actions -->
        <div class="settings-form-actions">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> Save Settings
          </button>
          <button type="button" id="resetButton" class="btn btn-outline-danger">
            <i class="fas fa-undo"></i> Reset to Defaults
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
<script src="/assets/js/pages/settings.js" type="module"></script>
{% endblock %} 