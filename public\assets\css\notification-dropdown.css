/* Notification Dropdown Styles */

/* Enhanced notification dropdown design */
.notification-dropdown {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #e5e7eb;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(0.95) translateY(-10px);
  opacity: 0;
  pointer-events: none;
  z-index: 9999;
}

/* Show dropdown */
.notification-dropdown.show,
.notification-dropdown:not(.hidden) {
  display: block !important;
  opacity: 1;
  transform: scale(1) translateY(0);
  pointer-events: auto;
}

/* Button hover effects */
.notification-dropdown-container:hover #notificationDropdownBtn,
#notificationDropdownBtn:hover {
  background-color: #f1f5f9;
  transform: scale(1.05);
  transition: all 0.2s ease;
}

.notification-dropdown-item {
  transition: all 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
  padding: 16px;
}

.notification-dropdown-item:hover {
  background-color: #f8fafc;
  transform: translateX(2px);
}

.notification-dropdown-item:last-child {
  border-bottom: none;
}

/* Unread notification styling */
.notification-dropdown-item.bg-blue-50 {
  background-color: #eff6ff;
  border-left: 4px solid #3b82f6;
  position: relative;
}

.notification-dropdown-item.bg-blue-50:hover {
  background-color: #dbeafe;
}

.notification-dropdown-item.bg-blue-50::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Notification badge pulse animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

#notificationBadge {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced dropdown animation and styling */
#notificationDropdown {
  transform-origin: top right;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
  pointer-events: none;
}

#notificationDropdown:not(.hidden) {
  opacity: 1;
  transform: scale(1) translateY(0);
  pointer-events: auto;
}

/* Keep dropdown open when hovering over it */
#notificationDropdown:hover {
  display: block !important;
  opacity: 1;
  transform: scale(1) translateY(0);
  pointer-events: auto;
}

/* Notification icon styling */
.notification-dropdown-item .material-symbols-outlined {
  font-size: 16px;
  font-weight: 400;
}

/* Scrollbar styling for notification list */
#notificationDropdownList::-webkit-scrollbar {
  width: 6px;
}

#notificationDropdownList::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

#notificationDropdownList::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

#notificationDropdownList::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  #notificationDropdown {
    width: 90vw;
    right: -10px;
  }
}

/* Fix spacing between notification and user dropdown */
.notification-dropdown-container {
  margin-right: 1rem;
}

/* Ensure proper positioning */
.notification-dropdown-container {
  position: relative;
  display: inline-block;
}

/* Additional button hover effects */
#notificationDropdownBtn:hover {
  background-color: #f1f5f9;
}

#notificationDropdownBtn:active {
  transform: scale(0.95);
}

/* Prevent right-click context menu */
body {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection in specific areas */
input, textarea, .selectable-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Disable right-click context menu */
* {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Re-enable text selection for form elements */
input[type="text"], input[type="password"], input[type="email"],
input[type="number"], input[type="search"], textarea,
.form-control, .notification-dropdown-item p {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Mark all read button styling */
.notification-dropdown-item button {
  transition: color 0.2s ease;
}

.notification-dropdown-item button:hover {
  color: #1d4ed8;
}

/* Empty state styling */
#notificationDropdownEmpty .material-symbols-outlined {
  font-size: 48px;
  color: #94a3b8;
}

/* Loading state styling */
#notificationDropdownLoading {
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Notification type indicators */
.notification-type-system {
  background-color: #f3f4f6;
  color: #374151;
}

.notification-type-lhdn {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.notification-type-announcement {
  background-color: #fef3c7;
  color: #d97706;
}

.notification-type-alert {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Notification priority indicators */
.notification-priority-high {
  border-left-color: #f59e0b;
}

.notification-priority-urgent {
  border-left-color: #ef4444;
}

.notification-priority-low {
  border-left-color: #10b981;
}

.notification-priority-normal {
  border-left-color: #6b7280;
}
