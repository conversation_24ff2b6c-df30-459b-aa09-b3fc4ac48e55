.modal-container {
    padding: 3rem;
    border-radius: 24px;
    background: #ffffff;
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(0, 0, 0, 0.05);
    text-align: center;
    max-width: 450px;
    width: 90%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-header {
    text-align: center;
    margin-bottom: 2rem;
}

.modal-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-icon svg {
    width: 32px;
    height: 32px;
}

.modal-icon.warning {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.modal-icon.info {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.modal-icon.success {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}




.modal-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.modal-button {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: none;
}

.modal-button.primary {
    background: #3b82f6;
    color: white;
    box-shadow: 0 1px 2px rgba(59, 130, 246, 0.2);
}

.modal-button.primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2);
}

.modal-button.secondary {
    background: #f1f5f9;
    color: #475569;
}

.modal-button.secondary:hover {
    background: #e2e8f0;
    transform: translateY(-1px);
}

.modal-help {
    margin-top: 1.5rem;
    text-align: center;
}

.modal-help a {
    color: #3b82f6;
    font-size: 0.875rem;
    text-decoration: none;
    transition: color 0.2s ease;
}

.modal-help a:hover {
    color: #2563eb;
    text-decoration: underline;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-16px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 640px) {
    .modal-container {
        padding: 2rem;
        width: 95%;
    }

    .modal-icon {
        width: 56px;
        height: 56px;
        margin-bottom: 1rem;
    }

    .modal-title {
        font-size: 1.25rem;
    }

    .modal-content {
        font-size: 0.875rem;
        margin-bottom: 1.5rem;
    }

    .modal-button {
        padding: 0.625rem 1.25rem;
    }
}

/* Loading Dialog */
.loading-dialog {
    padding: 3rem;
    border-radius: 24px;
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(0, 0, 0, 0.05);
    text-align: center;
    max-width: 450px;
    width: 90%;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.loading-content h5 {
    color: #1e293b;
    font-size: 1.35rem;
    font-weight: 600;
    margin: 0;
    letter-spacing: -0.02em;
}

/* Loading Steps */
.loading-steps {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    width: 100%;
    margin: 1rem 0;
}

.step {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    padding: 1rem 1.25rem;
    border-radius: 16px;
    background: rgba(248, 250, 252, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.step i {
    font-size: 1.5rem;
    color: #94a3b8;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-content {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
}

.step-title {
    font-weight: 600;
    color: #334155;
    font-size: 1rem;
    letter-spacing: -0.01em;
}

.step-status {
    font-size: 0.875rem;
    color: #64748b;
    letter-spacing: -0.01em;
}

/* Active Step */
.step.active {
    background: rgba(239, 246, 255, 0.8);
    border-color: rgba(147, 197, 253, 0.4);
    transform: translateY(-2px);
    box-shadow: 
        0 4px 6px -1px rgba(59, 130, 246, 0.1),
        0 2px 4px -1px rgba(59, 130, 246, 0.06);
}

.step.active i {
    color: #3b82f6;
}

.step.active .step-title {
    color: #1d4ed8;
}

.step.active .step-status {
    color: #3b82f6;
}

/* Completed Step */
.step.completed {
    background: rgba(240, 253, 244, 0.8);
    border-color: rgba(134, 239, 172, 0.4);
}

.step.completed i {
    color: #22c55e;
}

.step.completed .step-title {
    color: #15803d;
}

.step.completed .step-status {
    color: #22c55e;
}

/* Loading Spinner */
.loading-spinner {
    width: 52px;
    height: 52px;
    border: 3px solid rgba(226, 232, 240, 0.8);
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 0.8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    box-shadow: 
        0 4px 6px -1px rgba(59, 130, 246, 0.1),
        0 2px 4px -1px rgba(59, 130, 246, 0.06);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading Info */
.loading-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.25rem;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 14px;
    font-size: 0.875rem;
    color: #64748b;
    border: 1px solid rgba(226, 232, 240, 0.8);
    margin-top: 1rem;
}

.loading-info i {
    color: #3b82f6;
    font-size: 1.125rem;
}

/* Step Transitions */
.loading-steps .step {
    transition: all 0.3s ease;
}

.loading-steps .step[data-status="active"] {
    background: #eff6ff;
}

.loading-steps .step[data-status="done"] {
    background: #f0fdf4;
}

.loading-steps .step[data-status="error"] {
    background: #fef2f2;
}

