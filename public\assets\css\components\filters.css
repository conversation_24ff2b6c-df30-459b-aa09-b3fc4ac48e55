/* Filters Section */
.filters-section {
    background: #fff;
    border-radius: 0.75rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    margin-bottom: 1.5rem;
}

/* Header */
.filters-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-title {
    font-size: 1rem;
    font-weight: 600;
    color: #344767;
    display: flex;
    align-items: center;
}

/* Body */
.filters-body {
    padding: 1.5rem;
    display: grid;
    gap: 1.5rem;
    grid-template-columns: 2fr 1fr;
}

.main-filters {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.date-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.additional-filters {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    border-left: 1px solid #e5e7eb;
    padding-left: 1.5rem;
}

/* Footer */
.filters-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    background: #f9fafb;
    border-bottom-left-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
}

/* Form Elements */
.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.filter-group label i {
    color: #9ca3af;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.form-select, .form-control {
    height: 38px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    background-color: #fff;
    transition: all 0.2s ease;
}

.form-select:hover, .form-control:hover {
    border-color: #d1d5db;
}

.form-select:focus, .form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

/* Records Count */
.records-info {
    font-size: 0.875rem;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.records-info .api-notice {
    color: #856404;
    background: #fff3cd;
    padding: 0.25rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    border: 1px solid #ffeeba;
}

/* Responsive */
@media (max-width: 1024px) {
    .filters-body {
        grid-template-columns: 1fr;
    }

    .additional-filters {
        border-left: none;
        padding-left: 0;
        border-top: 1px solid #e5e7eb;
        padding-top: 1.5rem;
    }
}

@media (max-width: 768px) {
    .date-range {
        grid-template-columns: 1fr;
    }

    .filters-footer {
        flex-direction: column;
        gap: 1rem;
    }

    .filters-footer button {
        width: 100%;
    }

    .api-limit-badge {
        display: none; /* Hide in header on mobile */
    }
    
    .filters-footer {
        flex-direction: column;
    }
    
    .records-info {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
    }
    
    .records-info .api-notice {
        width: 100%;
        justify-content: center;
    }
}

/* Add after the filter-title styles */
.api-limit-badge {
    display: inline-flex;
    align-items: center;
    background: #fff3cd;
    color: #856404;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: normal;
    border: 1px solid #ffeeba;
}

.api-limit-badge i {
    font-size: 0.875rem;
    margin-right: 0.25rem;
}

.filter-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #344767;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.filters-body .row {
    margin: 0;
    padding: 1rem 0;
}

.filters-body .col-md-6 {
    padding: 1rem;
}

.border-end {
    border-right: 1px solid #e9ecef;
}

.api-notice {
    color: #664d03;
    background-color: #fff3cd;
    border: 1px solid #ffecb5;
    border-radius: 0.375rem;
    padding: 0.5rem;
    font-size: 0.875rem;
}

.records-info {
    font-size: 0.875rem;
    color: #6c757d;
}

.records-info span {
    display: inline-flex;
    align-items: center;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .filters-body .col-md-6 {
        border-right: none !important;
        border-bottom: 1px solid #e9ecef;
    }
    
    .filters-footer {
        flex-direction: column;
        gap: 1rem;
    }
    
    .action-buttons {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Active Filter Tags */
.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 0.5rem;
    border-radius: 0.5rem;
    background-color: #f8fafc;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    background-color: #1e40af;
    color: white;
    font-size: 0.813rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.filter-tag:hover {
    background-color: #1e3a8a;
}

.filter-tag .btn-close {
    padding: 0.25rem;
    margin-left: 0.25rem;
    font-size: 0.75rem;
    opacity: 0.75;
    transition: opacity 0.2s ease;
}

.filter-tag .btn-close:hover {
    opacity: 1;
}

.filter-tag .filter-text {
    white-space: nowrap;
}

#clearAllFilters {
    font-size: 0.813rem;
    padding: 0.25rem 0.5rem;
    text-decoration: none;
}

#clearAllFilters:hover {
    text-decoration: underline;
}

/* Filter Notes */
.filter-note.alert {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0;
}

.filter-note.alert-info {
    background-color: #f0f7ff;
    border: 1px solid #bfdbfe;
    color: #1e40af;
}

.filter-note.alert-warning {
    background-color: #fff8e6;
    border: 1px solid #fef0be;
    color: #92400e;
}

.filter-note.alert i {
    font-size: 1.125rem;
}

.filter-note.alert-info i {
    color: #3b82f6;
}

.filter-note.alert-warning i {
    color: #d97706;
}

.filter-note.alert small {
    font-size: 0.813rem;
    line-height: 1.5;
}

.filter-note.alert h6 {
    font-size: 0.875rem;
    font-weight: 600;
    color: inherit;
}

.filter-note.alert ul {
    margin-bottom: 0.5rem;
}

.filter-note.alert li {
    margin-bottom: 0.25rem;
}

.filter-note.alert .badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

.filter-note.alert .badge i {
    font-size: 0.875rem;
} 