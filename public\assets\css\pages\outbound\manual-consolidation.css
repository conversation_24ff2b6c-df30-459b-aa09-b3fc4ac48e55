/* Manual Consolidation Form Styling */

/* Modal styling */
.manual-consolidation-modal .modal-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.manual-consolidation-modal .modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

/* Card styling */
.manual-consolidation-card {
  border-radius: 0.5rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

/* Mode toggle styling */
.mode-toggle-card {
  background-color: #f8f9fc !important;
  border-radius: 0.5rem;
  border-left: 4px solid #4e73df;
}

.mode-toggle-switch .form-check-input {
  width: 3rem;
  height: 1.5rem;
}

.mode-toggle-switch .form-check-input:checked {
  background-color: #4e73df;
  border-color: #4e73df;
}

/* Section headers */
.section-heading {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

/* Line items table styling */
.line-items-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid #e3e6f0;
}

.line-items-table thead th {
  background-color: #f8f9fc;
  color: #5a5c69;
  font-weight: 600;
  border-bottom: 1px solid #e3e6f0;
  padding: 0.75rem;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.line-items-table tbody td {
  padding: 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid #e3e6f0;
}

.line-items-table tbody tr:last-child td {
  border-bottom: none;
}

.line-items-table tbody tr:hover {
  background-color: #f8f9fc;
}

/* Empty state for line items */
.line-items-empty {
  text-align: center;
  padding: 2rem;
  background-color: #f8f9fc;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.line-items-empty i {
  font-size: 2rem;
  color: #d1d3e2;
  margin-bottom: 1rem;
}

.line-items-empty p {
  color: #858796;
  margin-bottom: 0;
}

/* Add Line Item button - updated */
.outbound-action-btn {
  padding: 0.5rem 1.25rem;
  border-radius: 0.3rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border: none;
}

.outbound-action-btn.submit {
  background-color: #4e73df;
  color: white;
}

.outbound-action-btn.submit:hover {
  background-color: #375ad3;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
}

.outbound-action-btn.submit:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.outbound-action-btn.submit i {
  margin-right: 0.375rem;
  font-size: 0.875rem;
}

.outbound-action-btn.submit.disabled {
  background-color: #b7b9cc;
  cursor: not-allowed;
  opacity: 0.65;
}

/* System inputs (previously hidden) */
#manualTaxRate,
#manualTaxType,
#manualClassification {
  position: fixed;
  top: -9999px;
  left: -9999px;
  opacity: 0;
  height: 0;
  width: 0;
  z-index: -1;
}

/* Form check label spacing */
.form-check-label.m-1 {
  margin: 0.25rem !important;
  font-weight: 500;
  color: #555;
}

/* Line item actions */
.line-item-action {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #e74a3b;
  background-color: rgba(231, 74, 59, 0.1);
  border: none;
  cursor: pointer;
  transition: all 0.15s ease;
}

.line-item-action:hover {
  background-color: rgba(231, 74, 59, 0.2);
  color: #e74a3b;
}

/* Line items footer styling */
.line-items-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.25rem;
  background-color: #f8f9fc;
  border-top: 1px solid #e3e6f0;
  margin-top: 1rem;
}

.invoice-totals {
  display: flex;
  align-items: stretch;
  gap: 1rem;
  margin-left: auto;
}

.total-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 250px;
  padding: 1rem;
  background-color: #fff;
  border-radius: 0.375rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.total-item:last-child {
  background-color: #1e3a8a;
  color: white;
}

.total-label {
  font-size: 0.875rem;
  color: inherit;
  font-weight: 500;
  margin-bottom: 0.5rem;
  white-space: nowrap;
}

.total-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: inherit;
}

.total-value .currency-code {
  font-weight: 500;
  margin-right: 0.25rem;
}

.total-value .amount {
  font-weight: 600;
}

.total-value .converted {
  display: block;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  opacity: 0.9;
}

#addLineItemBtn {
  height: fit-content;
  margin-top: 0.5rem;
}

/* Form input styling */
.form-control.line-item-input {
  border-radius: 0.25rem;
  border-color: #e3e6f0;
  padding: 0.375rem 0.75rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control.line-item-input:focus {
  border-color: #bac8f3;
  box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.1);
}

.form-select.line-item-select {
  border-radius: 0.25rem;
  border-color: #e3e6f0;
  padding: 0.375rem 0.75rem;
  background-position: right 0.75rem center;
}

.form-select.line-item-select:focus {
  border-color: #bac8f3;
  box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.1);
} 