.loading-modal.fade {
    background: rgba(0, 0, 0, 0.5);
}

.loading-modal {
    background: rgba(0, 0, 0, 0.5);
}

.loading-modal .modal-content {
    background-color: #fff;
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-modal .modal-body {
    padding: 2rem;
}

.loading-content {
    text-align: center;
}

.loading-message {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 1.5rem;
}

.progress {
    height: 6px;
    border-radius: 3px;
    background-color: #e9ecef;
    margin-bottom: 2rem;
}

.progress-bar {
    background-color: #0d6efd;
    transition: width 0.3s ease;
}

.steps-container {
    text-align: left;
    max-width: 400px;
    margin: 0 auto;
}

.step {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.3s ease;
}


.step-icon {
    font-size: 0.8rem;
    margin-right: 0.75rem;
    color: #adb5bd;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}


.step[data-status="active"] {
    background-color: rgba(13, 110, 253, 0.1);
}

.step[data-status="in-progress"] .step-icon {
    color: #0d6efd;
    animation: spin 1s linear infinite;
}

.step[data-status="completed"] .step-icon {
    color: #198754;
}

.step-label {
    flex: 1;
    font-weight: 500;
    color: #495057;
}

.step-status {
    font-size: 0.875rem;
    color: #6c757d;
}

.step[data-status="active"] .step-status {
    color: #0d6efd;
    font-weight: 500;
}

.step[data-status="done"] .step-status {
    color: #198754;
}

.step-status.active {
    color: #0d6efd;
}

.step-status.success {
    color: #198754;
}

.step-status.waiting {
    color: #6c757d;
} 