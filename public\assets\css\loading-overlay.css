/* Loading overlay styles */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    border-radius: 0.5rem;
}

.card.loading {
    position: relative;
    min-height: 200px;
}

.loading-overlay .spinner-border {
    width: 3rem;
    height: 3rem;
}

.loading-overlay .mt-2 {
    margin-top: 0.5rem;
    font-weight: 500;
    color: #1e40af;
}

/* Loading backdrop for full screen loading */
.loading-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-backdrop .loading-content {
    background: white;
    padding: 2rem;
    border-radius: 0.5rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    min-width: 200px;
}

.loading-backdrop .loading-message {
    margin-top: 1rem;
    font-weight: 500;
    color: #1e40af;
}

/* Spin animation for refresh buttons */
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
