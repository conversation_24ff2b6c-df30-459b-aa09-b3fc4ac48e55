/* /**
* Template Name: NiceAdmin
* Template URL: https://bootstrapmade.com/nice-admin-bootstrap-admin-html-template/
* Updated: Apr 20 2024 with Bootstrap v5.3.3
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# General
--------------------------------------------------------------*/
:root {
  scroll-behavior: smooth;
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #51cf66;
  --danger-color: #ff6b6b;
  --info-color: #339af0;
  --warning-color: #ffd43b;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  
  --border-radius: 10px;
  --card-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  --transition: all 0.3s ease;
}

body {
  background: #f8f9fa;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    -webkit-font-smoothing: antialiased;
  color: #444444;
}

a {
  color: #4154f1;
  text-decoration: none;
}

a:hover {
  color: #717ff5;
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Nunito", sans-serif;
}

/*--------------------------------------------------------------
# Main
--------------------------------------------------------------*/
#main {
  padding: 40px 50px 80px 55px;
  transition: all 0.3s;
  font-size: 15px;
}

/*--------------------------------------------------------------
# Page Title
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Back to top button
--------------------------------------------------------------*/
.back-to-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: 15px;
  z-index: 99999;
  background: #4154f1;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  transition: all 0.4s;
}

.back-to-top i {
  font-size: 24px;
  color: #fff;
  line-height: 0;
}

.back-to-top:hover {
  background: #6776f4;
  color: #fff;
}

.back-to-top.active {
  visibility: visible;
  opacity: 1;
}

/*--------------------------------------------------------------
# Override some default Bootstrap stylings
--------------------------------------------------------------*/
/* Dropdown menus */
.dropdown-menu {
  border-radius: 4px;
  padding: 10px 0;
  animation-name: dropdown-animate;
  animation-duration: 0.2s;
  animation-fill-mode: both;
  border: 0;
  box-shadow: 0 5px 30px 0 rgba(82, 63, 105, 0.2);
}

.dropdown-menu .dropdown-header,
.dropdown-menu .dropdown-footer {
  text-align: center;
  font-size: 15px;
}

.dropdown-menu .dropdown-footer a {
  color: #444444;
  text-decoration: underline;
}

.dropdown-menu .dropdown-footer a:hover {
  text-decoration: none;
}

.dropdown-menu .dropdown-divider {
  color: #a5c5fe;
  margin: 0;
}

.dropdown-menu .dropdown-item {
  font-size: 14px;
  padding: 10px 15px;
  transition: 0.3s;
}

.dropdown-menu .dropdown-item i {
  margin-right: 10px;
  font-size: 18px;
  line-height: 0;
}

.dropdown-menu .dropdown-item:hover {
  background-color: #f6f9ff;
}

@media (min-width: 768px) {
  .dropdown-menu-arrow::before {
    content: "";
    width: 13px;
    height: 13px;
    background: #fff;
    position: absolute;
    top: -7px;
    right: 20px;
    transform: rotate(45deg);
    border-top: 1px solid #eaedf1;
    border-left: 1px solid #eaedf1;
  }
}

@keyframes dropdown-animate {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }

  0% {
    opacity: 0;
  }
}

/* Light Backgrounds */
.bg-primary-light {
  background-color: #cfe2ff;
  border-color: #cfe2ff;
}

.bg-secondary-light {
  background-color: #e2e3e5;
  border-color: #e2e3e5;
}

.bg-success-light {
  background-color: #d1e7dd;
  border-color: #d1e7dd;
}

.bg-danger-light {
  background-color: #f8d7da;
  border-color: #f8d7da;
}

.bg-warning-light {
  background-color: #fff3cd;
  border-color: #fff3cd;
}

.bg-info-light {
  background-color: #cff4fc;
  border-color: #cff4fc;
}

.bg-dark-light {
  background-color: #d3d3d4;
  border-color: #d3d3d4;
}

/* Card */

  /* CSS for the colored bar at the top */
  .color-bar-outbound {
    width: 100%;
    height: 30px; /* Adjust the height as needed */
    background-color: #ff9100; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .color-bar-inbound {
    width: 100%;
    height: 30px; /* Adjust the height as needed */
    background-color: #37d34c; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .color-bar-company {
    width: 100%;
    height: 30px; /* Adjust the height as needed */
    background-color: #3f51b5; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .color-bar-total-invoice {
    width: 100%;
    height: 30px; /* Adjust the height as needed */
    background-color: #3ecd3e; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;  
  }
  .color-bar-submitted {
    width: 100%;
    height: 30px; /* Adjust the height as needed */
    background-color: #3f51b5; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .color-bar-pending {
    width: 100%;
    height: 30px; /* Adjust the height as needed */
    background-color: #ffb107; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .color-bar-cancel {
    width: 100%;
    height: 30px; /* Adjust the height as needed */
    background-color: #f7d200; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .color-bar-invalid {
    width: 100%;
    height: 30px; /* Adjust the height as needed */
    background-color: #f70000; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .color-bar-rejected {
    width: 100%;
    height: 30px; /* Adjust the height as needed */
    background-color: #f74200; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .color-bar-queue {
    width: 100%;
    height: 30px; /* Adjust the height as needed */
    background-color: #5e5e5e; 
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  
.card {
  margin-bottom: 30px;
  border: none;
  border-radius: 5px;
  box-shadow: 0px 0 30px rgba(1, 41, 112, 0.1);
}

.card-header,
.card-footer {
  border-color: #ebeef4;
  background-color: #fff;
  color: #798eb3;
  padding: 15px;
}

.card-title {
  padding: 20px 0 15px 0;
  font-size: 15px;
  font-weight: 500;
  color: #012970;
  font-family: "Poppins", sans-serif;
  
}

.card-title span {
  color: #899bbd;
  font-size: 14px;
  font-weight: 400;
}

.card-body {
  padding: 0 20px 20px 20px;
}

.card-body-reports {
  height: 450px;
  padding: 0 20px 20px 20px;
}

.card-img-overlay {
  background-color: rgba(255, 255, 255, 0.6);
}

/* Alerts */
.alert-heading {
  font-weight: 500;
  font-family: "Poppins", sans-serif;
  font-size: 20px;
}

.section.dashboard {
  padding: 20px; /* add some padding to the section ` */
}

.card {
  margin-bottom: 20px; /* add some margin between the cards */
}

/* Accordion */
.accordion-item {
  border: 1px solid #ebeef4;
}

.accordion-button .icon {
  margin-right: 10px;
}

.accordion-button.valid .icon {
  color: green;
}

.accordion-button.invalid .icon {
  color: red;
}

.accordion-header {
  margin-bottom: 5px;
}

.accordion-button:focus {
  outline: 0;
  box-shadow: none;
}

.accordion-button:not(.collapsed) {
  color: #012970;
  background-color: #f6f9ff;
}

.accordion-flush .accordion-button {
  padding: 15px 0;
  background: none;
  border: 0;
}

.accordion-flush .accordion-button:not(.collapsed) {
  box-shadow: none;
  color: #4154f1;
}

.accordion-flush .accordion-body {
  padding: 0 0 15px 0;
  color: #3e4f6f;
  font-size: 15px;
}

/* Breadcrumbs */
.breadcrumb {
  font-size: 14px;
  font-family: "Nunito", sans-serif;
  color: #899bbd;
  font-weight: 600;
}

.breadcrumb a {
  color: #899bbd;
  transition: 0.3s;
}

.breadcrumb a:hover {
  color: #51678f;
}

.breadcrumb .breadcrumb-item::before {
  color: #899bbd;
}

.breadcrumb .active {
  color: #51678f;
  font-weight: 600;
}

/* Bordered Tabs */
.nav-tabs-bordered {
  border-bottom: 2px solid #ebf4ed;
}

.nav-tabs-bordered .nav-link {
  margin-bottom: -2px;
  border: none;
  color: #2c384e;
}

.nav-tabs-bordered .nav-link:hover,
.nav-tabs-bordered .nav-link:focus {
  color: #4154f1;
}

.nav-tabs-bordered .nav-link.active {
  background-color: #fff;
  color: #4154f1;
  border-bottom: 2px solid #4154f1;
}

.top-menu-wrap {
  margin-left: 20px; /* adjust the margin to your liking */
}

.top-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
}

.top-menu li {
  margin-right: 20px; /* adjust the margin to your liking */
}

.top-menu a {
  color: #333; /* adjust the color to your liking */
  text-decoration: none;
}

.top-menu a:hover {
  color: #012970; /* adjust the hover color to your liking */
}

/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/
.logo {
  line-height: 5;
}

@media (min-width: 1200px) {
  .logo {
    width: 260px;
    margin-bottom: 3px;
  }
}

.logo img {
  max-height: 80px;
  margin-right: 6px;

}

.logo span {
  font-size: 26px;
  font-weight: 700;
  color: #012970;
  font-family: "Nunito", sans-serif;
}

.header {
  transition: all 0.5s;
  z-index: 997;
  height: 60px;
  box-shadow: 0px 2px 20px rgba(1, 41, 112, 0.1);
  background-color: #fff;
  padding-left: 20px;
}

.header .toggle-sidebar-btn {
  font-size: 32px;
  padding-left: 10px;
  cursor: pointer;
  color: #012970;
}

.header .logo {
  line-height: 1;
}

.header .logo img {
  max-height: 26px;
  margin-right: 6px;
}

.header .logo span {
  font-size: 26px;
  font-weight: 700;
  color: #012970;
  font-family: "Nunito", sans-serif;
}

/* Navigation Menu */
.top-menu {
  padding: 0;
  margin: 0;
  list-style: none;
}

.top-menu li {
  position: relative;
  white-space: nowrap;
}

.top-menu a {
  display: flex;
  align-items: center;
  color: #012970;
  transition: 0.3s;
  text-decoration: none;
}

.top-menu a:hover,
.top-menu .active {
  color: #4154f1;
}

.top-menu a:focus {
  outline: none;
}

/* Profile */
.nav-profile {
  color: #012970;
}

.nav-profile img {
  max-height: 36px;
}

.nav-profile span {
  font-size: 14px;
  font-weight: 600;
}

.profile .dropdown-header h6 {
  font-size: 18px;
  margin-bottom: 0;
  font-weight: 600;
  color: #444444;
}

.profile .dropdown-header span {
  font-size: 14px;
}

.profile .dropdown-item {
  font-size: 14px;
  padding: 10px 15px;
  transition: 0.3s;
}

.profile .dropdown-item i {
  margin-right: 10px;
  font-size: 18px;
  line-height: 0;
}

.profile .dropdown-item:hover {
  background-color: #f6f9ff;
}

/* Responsive */
@media (max-width: 768px) {
  .header .logo span {
    display: none;
  }
  
  .top-menu a {
    padding: 5px;
    font-size: 0.9rem;
  }
}

@media (min-width: 1200px) {
  .logo {
    width: 260px;
    margin-bottom: 3px;
  }
}

.logo img {
  max-height: 80px;
  margin-right: 6px;

}

.logo span {
  font-size: 26px;
  font-weight: 700;
  color: #012970;
  font-family: "Nunito", sans-serif;
}

.header {
  transition: all 0.5s;
  z-index: 997;
  height: 75px;
  margin-bottom: 50px;
  box-shadow: 0px 2px 20px rgba(1, 41, 112, 0.1);
  background-color: #fff;
  padding-left: 20px;

  /* Toggle Sidebar Button */
  /* Search Bar */
}

.header .toggle-sidebar-btn {
  font-size: 32px;
  padding-left: 10px;
  cursor: pointer;
  color: #012970;
}

.header .search-bar {
  min-width: 360px;
  padding: 0 20px;
}

@media (max-width: 1199px) {
  .header .search-bar {
    position: fixed;
    top: 50px;
    left: 0;
    right: 0;
    padding: 20px;
    box-shadow: 0px 0px 15px 0px rgba(1, 41, 112, 0.1);
    background: white;
    z-index: 9999;
    transition: 0.3s;
    visibility: hidden;
    opacity: 0;
  }

  .header .search-bar-show {
    top: 60px;
    visibility: visible;
    opacity: 1;
  }
}

.header .search-form {
  width: 100%;
}

.header .search-form input {
  border: 0;
  font-size: 14px;
  color: #012970;
  border: 1px solid rgba(1, 41, 112, 0.2);
  padding: 7px 38px 7px 8px;
  border-radius: 3px;
  transition: 0.3s;
  width: 100%;
}

.header .search-form input:focus,
.header .search-form input:hover {
  outline: none;
  box-shadow: 0 0 10px 0 rgba(1, 41, 112, 0.15);
  border: 1px solid rgba(1, 41, 112, 0.3);
}

.header .search-form button {
  border: 0;
  padding: 0;
  margin-left: -30px;
  background: none;
}

.header .search-form button i {
  color: #012970;
}

/*--------------------------------------------------------------
# Header Nav
--------------------------------------------------------------*/
.header-nav ul {
  list-style: none;
}

.header-nav>ul {
  margin: 0;
  padding: 0;
}

.header-nav .nav-icon {
  font-size: 22px;
  color: #012970;
  margin-right: 25px;
  position: relative;
}

.header-nav .nav-profile {
  color: #012970;
}

.header-nav .nav-profile img {
  max-height: 36px;
}

.header-nav .nav-profile span {
  font-size: 14px;
  font-weight: 600;
}

.header-nav .badge-number {
  position: absolute;
  inset: -2px -5px auto auto;
  font-weight: normal;
  font-size: 12px;
  padding: 3px 6px;
}

.header-nav .notifications {
  inset: 8px -15px auto auto !important;
}

.header-nav .notifications .notification-item {
  display: flex;
  align-items: center;
  padding: 15px 10px;
  transition: 0.3s;
}

.header-nav .notifications .notification-item i {
  margin: 0 20px 0 10px;
  font-size: 24px;
}

.header-nav .notifications .notification-item h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.header-nav .notifications .notification-item p {
  font-size: 13px;
  margin-bottom: 3px;
  color: #919191;
}

.header-nav .notifications .notification-item:hover {
  background-color: #f6f9ff;
}

.header-nav .messages {
  inset: 8px -15px auto auto !important;
}

.header-nav .messages .message-item {
  padding: 15px 10px;
  transition: 0.3s;
}

.header-nav .messages .message-item a {
  display: flex;
}

.header-nav .messages .message-item img {
  margin: 0 20px 0 10px;
  max-height: 40px;
}

.header-nav .messages .message-item h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
  color: #444444;
}

.header-nav .messages .message-item p {
  font-size: 13px;
  margin-bottom: 3px;
  color: #919191;
}

.header-nav .messages .message-item:hover {
  background-color: #f6f9ff;
}

.header-nav .profile {
  min-width: 240px;
  padding-bottom: 0;
  top: 8px !important;
}

.header-nav .profile .dropdown-header h6 {
  font-size: 18px;
  margin-bottom: 0;
  font-weight: 600;
  color: #444444;
}

.header-nav .profile .dropdown-header span {
  font-size: 14px;
}

.header-nav .profile .dropdown-item {
  font-size: 14px;
  padding: 10px 15px;
  transition: 0.3s;
}

.header-nav .profile .dropdown-item i {
  margin-right: 10px;
  font-size: 18px;
  line-height: 0;
}

.header-nav .profile .dropdown-item:hover {
  background-color: #f6f9ff;
}

/*--------------------------------------------------------------
# Sidebar
--------------------------------------------------------------*/
.sidebar {
  position: fixed;
  top: 60px;
  left: 0;
  bottom: 0;
  width: 300px;
  z-index: 996;
  transition: all 0.3s;
  padding: 20px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #aab7cf transparent;
  box-shadow: 0px 0px 20px rgba(1, 41, 112, 0.1);
  background-color: #fff;
}

@media (max-width: 1199px) {
  .sidebar {
    left: -300px;
  }
}

.sidebar::-webkit-scrollbar {
  width: 5px;
  height: 8px;
  background-color: #fff;
}

.sidebar::-webkit-scrollbar-thumb {
  background-color: #aab7cf;
}

@media (min-width: 1200px) {

  #main,
  #footer {
    /* margin-left: 300px; */
  }
}

@media (max-width: 1199px) {
  .toggle-sidebar .sidebar {
    left: 0;
  }
}

@media (min-width: 1200px) {

  .toggle-sidebar #main,
  .toggle-sidebar #footer {
    margin-left: 0;
  }

  .toggle-sidebar .sidebar {
    left: -300px;
  }
}

.sidebar-nav {
  padding: 0;
  margin: 0;
  list-style: none;
}

.sidebar-nav li {
  padding: 0;
  margin: 0;
  list-style: none;
}

.sidebar-nav .nav-item {
  margin-bottom: 5px;
}

.sidebar-nav .nav-heading {
  font-size: 11px;
  text-transform: uppercase;
  color: #899bbd;
  font-weight: 600;
  margin: 10px 0 5px 15px;
}

.sidebar-nav .nav-link {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 600;
  color: #4154f1;
  transition: 0.3;
  background: #f6f9ff;
  padding: 10px 15px;
  border-radius: 4px;
}

.sidebar-nav .nav-link i {
  font-size: 16px;
  margin-right: 10px;
  color: #4154f1;
}

.sidebar-nav .nav-link.collapsed {
  color: #012970;
  background: #fff;
}

.sidebar-nav .nav-link.collapsed i {
  color: #899bbd;
}

.sidebar-nav .nav-link:hover {
  color: #4154f1;
  background: #f6f9ff;
}

.sidebar-nav .nav-link:hover i {
  color: #4154f1;
}

.sidebar-nav .nav-link .bi-chevron-down {
  margin-right: 0;
  transition: transform 0.2s ease-in-out;
}

.sidebar-nav .nav-link:not(.collapsed) .bi-chevron-down {
  transform: rotate(180deg);
}

.sidebar-nav .nav-content {
  padding: 5px 0 0 0;
  margin: 0;
  list-style: none;
}

.sidebar-nav .nav-content a {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #012970;
  transition: 0.3;
  padding: 10px 0 10px 40px;
  transition: 0.3s;
}

.sidebar-nav .nav-content a i {
  font-size: 6px;
  margin-right: 8px;
  line-height: 0;
  border-radius: 50%;
}

.sidebar-nav .nav-content a:hover,
.sidebar-nav .nav-content a.active {
  color: #4154f1;
}

.sidebar-nav .nav-content a.active i {
  background-color: #4154f1;
}

/*--------------------------------------------------------------
# Dashboard
--------------------------------------------------------------*/
/* Filter dropdown */
.dashboard .filter {
  position: absolute;
  right: 0px;
  top: 5px;
}

.dashboard .filter .icon {
  color: #ffffff;
  padding-right: 20px;
  padding-bottom: 0px;
  transition: 0.3s;
  font-size: 16px;
}

.dashboard .filter .icon:hover,
.dashboard .filter .icon:focus {
  color: #a2a2a2;
}

.dashboard .filter .dropdown-header {
  padding: 8px 15px;
}

.dashboard .filter .dropdown-header h6 {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 1px;
  color: #aab7cf;
  margin-bottom: 0;
  padding: 0;
}

.dashboard .filter .dropdown-item {
  padding: 8px 15px;
}

/* Info Cards */
.dashboard .info-card {
  padding-bottom: 10px;
}

.dashboard .info-card h6 {
  font-size: 28px;
  color: #012970;
  font-weight: 700;
  margin: 0;
  padding: 0;
}

.dashboard .card-icon {
  font-size: 32px;
  line-height: 0;
  width: 64px;
  height: 64px;
  flex-shrink: 0;
  flex-grow: 0;
}

.important {
  color: red; /* You can change this color to whatever you prefer */
}

.dashboard .sales-card .card-icon {
  color: #4154f1;
  background: #f6f6fe;
}

.dashboard .revenue-card .card-icon {
  color: #2eca6a;
  background: #e0f8e9;
}

.dashboard .customers-card .card-icon {
  color: #ff771d;
  background: #ffecdf;
}

.dashboard .cancel-card .card-icon {
  color: #f7d200;
  background: #ffecdf;
}

.dashboard .rejected-card .card-icon {
  color: red;
  background: #ffecdf;
}

.dashboard .queue-card .card-icon {
  color: rgb(90, 90, 90);
  background: #c6cbcb;
}

/* Activity */
.dashboard .activity {
  font-size: 14px;
}

.dashboard .activity .activity-item .activite-label {
  color: #888;
  position: relative;
  flex-shrink: 0;
  flex-grow: 0;
  min-width: 64px;
}

.dashboard .activity .activity-item .activite-label::before {
  content: "";
  position: absolute;
  right: -11px;
  width: 4px;
  top: 0;
  bottom: 0;
  background-color: #eceefe;
}

.dashboard .activity .activity-item .activity-badge {
  margin-top: 3px;
  z-index: 1;
  font-size: 11px;
  line-height: 0;
  border-radius: 50%;
  flex-shrink: 0;
  border: 3px solid #fff;
  flex-grow: 0;
}

.dashboard .activity .activity-item .activity-content {
  padding-left: 10px;
  padding-bottom: 20px;
}

.dashboard .activity .activity-item:first-child .activite-label::before {
  top: 5px;
}

.dashboard .activity .activity-item:last-child .activity-content {
  padding-bottom: 0;
}

/* News & Updates */
.dashboard .news .post-item+.post-item {
  margin-top: 15px;
}

.dashboard .news img {
  width: 80px;
  float: left;
  border-radius: 5px;
}

.dashboard .news h4 {
  font-size: 15px;
  margin-left: 95px;
  font-weight: bold;
  margin-bottom: 5px;
}

.dashboard .news h4 a {
  color: #012970;
  transition: 0.3s;
}

.dashboard .news h4 a:hover {
  color: #4154f1;
}

.dashboard .news p {
  font-size: 14px;
  color: #777777;
  margin-left: 95px;
}


/*--------------------------------------------------------------
# Icons list page
--------------------------------------------------------------*/
.iconslist {
  display: grid;
  max-width: 100%;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1.25rem;
  padding-top: 15px;
}

.iconslist .icon {
  background-color: #fff;
  border-radius: 0.25rem;
  text-align: center;
  color: #012970;
  padding: 15px 0;
}

.iconslist i {
  margin: 0.25rem;
  font-size: 2.5rem;
}

.iconslist .label {
  font-family: var(--bs-font-monospace);
  display: inline-block;
  width: 100%;
  overflow: hidden;
  padding: 0.25rem;
  font-size: 12px;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #666;
}

/*--------------------------------------------------------------
# Profie Page
--------------------------------------------------------------*/
.profile .profile-card img {
  max-width: 120px;
}

.profile .profile-card h2 {
  font-size: 24px;
  font-weight: 700;
  color: #2c384e;
  margin: 10px 0 0 0;
}

.profile .profile-card h3 {
  font-size: 18px;
}

.profile .profile-card .social-links a {
  font-size: 20px;
  display: inline-block;
  color: rgba(1, 41, 112, 0.5);
  line-height: 0;
  margin-right: 10px;
  transition: 0.3s;
}

.profile .profile-card .social-links a:hover {
  color: #012970;
}

.profile .profile-overview .row {
  margin-bottom: 20px;
  font-size: 15px;
}

.profile .profile-overview .card-title {
  color: #012970;
}

.profile .profile-overview .label {
  font-weight: 600;
  color: rgba(1, 41, 112, 0.6);
}

.profile .profile-edit label {
  font-weight: 600;
  color: rgba(1, 41, 112, 0.6);
}

.profile .profile-edit img {
  max-width: 120px;
}

/*--------------------------------------------------------------
# F.A.Q Page
--------------------------------------------------------------*/
.faq .basic h6 {
  font-size: 18px;
  font-weight: 600;
  color: #4154f1;
}

.faq .basic p {
  color: #6980aa;
}

/*--------------------------------------------------------------
# Contact
--------------------------------------------------------------*/
.contact .info-box {
  padding: 28px 30px;
}

.contact .info-box i {
  font-size: 38px;
  line-height: 0;
  color: #4154f1;
}

.contact .info-box h3 {
  font-size: 20px;
  color: #012970;
  font-weight: 700;
  margin: 20px 0 10px 0;
}

.contact .info-box p {
  padding: 0;
  line-height: 24px;
  font-size: 14px;
  margin-bottom: 0;
}

.contact .php-email-form .error-message {
  display: none;
  color: #fff;
  background: #ed3c0d;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.contact .php-email-form .sent-message {
  display: none;
  color: #fff;
  background: #18d26e;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.contact .php-email-form .loading {
  display: none;
  background: #fff;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.contact .php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid #18d26e;
  border-top-color: #eee;
  animation: animate-loading 1s linear infinite;
}

.contact .php-email-form input,
.contact .php-email-form textarea {
  border-radius: 0;
  box-shadow: none;
  font-size: 14px;
  border-radius: 0;
}

.contact .php-email-form input:focus,
.contact .php-email-form textarea:focus {
  border-color: #4154f1;
}

.contact .php-email-form input {
  padding: 10px 15px;
}

.contact .php-email-form textarea {
  padding: 12px 15px;
}

.contact .php-email-form button[type=submit] {
  background: #4154f1;
  border: 0;
  padding: 10px 30px;
  color: #fff;
  transition: 0.4s;
  border-radius: 4px;
}

.contact .php-email-form button[type=submit]:hover {
  background: #5969f3;
}

@keyframes animate-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Error 404
--------------------------------------------------------------*/
.error-404 {
  padding: 30px;
}

.error-404 h1 {
  font-size: 180px;
  font-weight: 700;
  color: #4154f1;
  margin-bottom: 0;
  line-height: 150px;
}

.error-404 h2 {
  font-size: 24px;
  font-weight: 700;
  color: #012970;
  margin-bottom: 30px;
}

.error-404 .btn {
  background: #51678f;
  color: #fff;
  padding: 8px 30px;
}

.error-404 .btn:hover {
  background: #3e4f6f;
}

@media (min-width: 992px) {
  .error-404 img {
    max-width: 50%;
  }
}

/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
.footer {
  padding: 40px 0 30px;
  font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;

}

.footer .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.footer .copyright {
  text-align: center;
  color: #334155;
  font-weight: 400;
  letter-spacing: 0.01em;
  margin-bottom: 0.5rem;
}

.footer .credits {
  text-align: center;
  font-size: 0.75rem;
  color: #64748b;
  letter-spacing: 0.01em;
}

.footer a {
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer a:hover {
  color: #1d4ed8;
}

@media (min-width: 768px) {
  .footer {
    padding: 50px 0 40px;
  }
  
  .footer .copyright,
  .footer .credits {
    display: inline-block;
    margin: 0 1rem;
  }
}

.vertical-dots {
  display: inline-block;
  width: 4px;
  height: 4px;
  background-color: #6776f4;
  border-radius: 50%;
  margin: 2px 0;
}

.dots-button {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
}


.profile .dropdown-header .d-flex {
  align-items: center;
}



.welcome-icon {
  background: rgba(255, 255, 255, 0.2);
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.welcome-icon i {
  font-size: 24px;
}

.welcome-content h4 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.welcome-content p {
  opacity: 0.9;
  margin-top: 0.25rem;
}

.btn-lhdn-danger {
  background-color: #ed3c0d;
  border-color: #ed3c0d;
  color: white;
}


/* LHDN Button Styling */
.btn-lhdn {
  background-color: #1a3a6c;
  border-color: #1a3a6c;
  color: white;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 200;
  box-shadow: 0 2px 10px rgba(26, 58, 108, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn-lhdn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2), rgba(255,255,255,0.1));
  transition: all 0.6s ease;
  z-index: -1;
}

.btn-lhdn:hover {
  background-color: #0f2952;
  border-color: #0f2952;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(26, 58, 108, 0.3);
}

.btn-lhdn:hover:before {
  left: 100%;
}

.btn-lhdn:active {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(26, 58, 108, 0.2);
}

.btn-lhdn i {
  transition: transform 0.3s ease;
}

.btn-lhdn:hover i {
  transform: translateX(3px);
}

/* TIN Validation styles */

/* Cooldown indicator styles */
.history-item {
  position: relative;
  transition: all 0.2s;
}

.history-item:not(.on-cooldown):hover {
  background-color: rgba(13, 110, 253, 0.05);
  cursor: pointer;
}

.history-item.on-cooldown {
  opacity: 0.7;
  cursor: not-allowed;
}

.cooldown-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  background-color: #fd7e14;
  color: white;
  z-index: 2;
}

/* Session count badge styles */
.validation-count-badge {
  transition: background-color 0.3s;
}

.badge-low {
  background-color: #0d6efd;
}

.badge-medium {
  background-color: #fd7e14;
}

.badge-high {
  background-color: #dc3545;
}

/* Search input styles */
.validation-search-container {
  position: relative;
  max-width: 200px;
}

.validation-search-container .search-icon {
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 0.8rem;
}

.validation-search-container input {
  padding-left: 30px;
  font-size: 0.85rem;
  border-radius: 50px;
}

/* Animation for new history items */
@keyframes highlightNew {
  0% { background-color: rgba(13, 110, 253, 0.2); }
  100% { background-color: transparent; }
}

.history-item.new-item {
  animation: highlightNew 1.5s ease-out;
}

/* Empty state styling */
.empty-history-message {
  color: #6c757d;
  text-align: center;
  padding: 1.5rem;
  font-style: italic;
}

/* Tooltip styling */
.info-tooltip {
  cursor: pointer;
  color: #6c757d;
  margin-left: 5px;
  font-size: 0.9rem;
  transition: color 0.2s;
}

.info-tooltip:hover {
  color: #0d6efd;
}

/* No results found styling */
.no-results-message {
  font-style: italic;
  color: #6c757d;
  text-align: center;
  padding: 1rem;
}

/* Warning message styling */
.warning-message {
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #664d03;
  border-radius: 0.25rem;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* TIN Validation Results styling */
.validation-result {
  transition: all 0.3s ease;
}

.validation-result.valid {
  border-left: 4px solid #198754;
}

.validation-result.invalid {
  border-left: 4px solid #dc3545;
}

.validation-result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.validation-result-content {
  padding: 1rem;
} 

/* TIN Validation Toast styling */
.validation-toast {
  position: fixed;
  top: 1rem;
  right: 1rem;
  background-color: #ffffff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #0d6efd;
  animation: slideInToast 0.3s ease-out;
  z-index: 1050;
}

.validation-toast .toast-header {
  background-color: rgba(13, 110, 253, 0.05);
  border-bottom: 1px solid rgba(13, 110, 253, 0.1);
}

.validation-toast .toast-body {
  padding: 0.75rem 1rem;
}

@keyframes slideInToast {
  from {
      transform: translateY(-100%);
      opacity: 0;
  }
  to {
      transform: translateY(0);
      opacity: 1;
  }
}

