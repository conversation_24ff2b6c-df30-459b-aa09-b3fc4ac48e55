{% extends '../../layout.html' %}

{% block head %}
<title>eInvoice Settings - eInvoice Portal</title>
<link href="/assets/css/pages/settings.css" rel="stylesheet">
<link href="/assets/css/pages/profile.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="settings-container">
  <!-- Header -->
  <div class="profile-welcome-card">
    <h2>
      <i class="fas fa-file-invoice"></i>
      eInvoice Settings
    </h2>
    <p>Configure your eInvoice preferences and integration settings</p>
  </div>

  <!-- Settings Content -->
  <div class="settings-content">
    <div class="settings-grid">
      <!-- API Configuration -->
      <section class="settings-section">
        <h2>
          <i class="fas fa-plug me-2"></i>
          API Configuration
        </h2>
        <div class="settings-form">
          <div class="form-group">
            <label>API Endpoint URL</label>
            <input type="text" id="apiEndpoint" class="form-control" placeholder="https://api.example.com/v1">
          </div>
          <div class="form-group">
            <label>API Key</label>
            <div class="input-group">
              <input type="password" id="apiKey" class="form-control" placeholder="Enter your API key">
              <button class="btn btn-outline-secondary" type="button" onclick="toggleApiKeyVisibility()">
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>
          <div class="form-group">
            <label>API Version</label>
            <select id="apiVersion" class="form-control">
              <option value="v1">Version 1.0</option>
              <option value="v2">Version 2.0</option>
            </select>
          </div>
        </div>
      </section>

      <!-- Document Templates -->
      <section class="settings-section">
        <h2>
          <i class="fas fa-file-alt me-2"></i>
          Document Templates
        </h2>
        <div class="settings-form">
          <div class="form-group">
            <label>Default Template</label>
            <select id="defaultTemplate" class="form-control">
              <option value="standard">Standard Invoice</option>
              <option value="detailed">Detailed Invoice</option>
              <option value="simple">Simple Invoice</option>
            </select>
          </div>
          <div class="form-group">
            <label>Logo Position</label>
            <select id="logoPosition" class="form-control">
              <option value="top-left">Top Left</option>
              <option value="top-right">Top Right</option>
              <option value="center">Center</option>
            </select>
          </div>
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" id="showQRCode">
              Include QR Code
            </label>
          </div>
        </div>
      </section>

      <!-- Numbering Format -->
      <section class="settings-section">
        <h2>
          <i class="fas fa-list-ol me-2"></i>
          Numbering Format
        </h2>
        <div class="settings-form">
          <div class="form-group">
            <label>Invoice Number Format</label>
            <input type="text" id="invoiceFormat" class="form-control" placeholder="INV-{YYYY}-{MM}-{0000}">
            <small class="text-muted">Available placeholders: {YYYY}, {MM}, {DD}, {0000}</small>
          </div>
          <div class="form-group">
            <label>Starting Number</label>
            <input type="number" id="startingNumber" class="form-control" min="1">
          </div>
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" id="resetMonthly">
              Reset Counter Monthly
            </label>
          </div>
        </div>
      </section>

      <!-- Tax Settings -->
      <section class="settings-section">
        <h2>
          <i class="fas fa-percent me-2"></i>
          Tax Settings
        </h2>
        <div class="settings-form">
          <div class="form-group">
            <label>Default Tax Rate (%)</label>
            <input type="number" id="defaultTaxRate" class="form-control" step="0.01" min="0">
          </div>
          <div class="form-group">
            <label>Tax Registration Number</label>
            <input type="text" id="taxRegNumber" class="form-control" readonly>
          </div>
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" id="includeTax">
              Show Tax Details on Invoice
            </label>
          </div>
        </div>
      </section>
    </div>

    <!-- Save Button -->
    <div class="settings-actions">
      <button type="button" class="btn btn-secondary" onclick="resetSettings()">
        <i class="fas fa-undo me-2"></i>
        Reset
      </button>
      <button type="button" class="btn btn-primary" onclick="saveEInvoiceSettings()">
        <i class="fas fa-save me-2"></i>
        Save Changes
      </button>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/assets/js/pages/settings/einvoice.js"></script>
{% endblock %} 