<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classic Invoice</title>
    <style>
        :root {
            --primary-blue: #1e3a8a;
            --secondary-blue: #2563eb;
            --light-bg: #f8fafc;
            --border-color: #e5e7eb;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;

        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--primary-blue);
        }

        .logo {
            max-width: 200px;
            margin-bottom: 20px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .info-section {
            background: var(--light-bg);
            padding: 20px;
            border-radius: 8px;
        }

        h2 {
            color: var(--primary-blue);
            font-size: 18px;
            margin: 0 0 15px 0;
        }

        p {
            margin: 5px 0;
            font-size: 14px;
            line-height: 1.5;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
        }

        th {
            background: var(--primary-blue);
            color: white;
            padding: 12px;
            text-align: left;
            font-size: 14px;
        }

        td {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
        }

        .text-right {
            text-align: right;
        }

        .amount-box {
            background: var(--light-bg);
            padding: 20px;
            border-radius: 8px;
            width: 300px;
            margin-left: auto;
        }

        .amount-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            font-size: 14px;
        }

        .total-row {
            border-top: 2px solid var(--border-color);
            margin-top: 8px;
            padding-top: 8px;
            font-weight: bold;
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .qr-code {
            width: 100px;
            height: 100px;
            padding: 8px;
            background: var(--light-bg);
            border-radius: 8px;
        }

        .footer-text {
            font-size: 12px;
            color: #666;
        }

        @media print {
            body {
                padding: 0;
            }
            .container {
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{:CompanyLogo}}" alt="Company Logo" class="logo" onerror="this.onerror=null; this.src='{{:CompanyLogo}}';">
            <div class="e-invoice-text">E-INVOICE</div>
            <h1>{{:CompanyName}}</h1>
            <p>{{:SupplierAddress}}</p>
            <p>{{:SupplierContactNo}} | {{:SupplierEmail}}</p>
        </div>

        <div class="info-grid">
            <div class="info-section">
                <h2>Supplier Information</h2>
                <p><strong>Company Name:</strong> {{:SupplierName}}</p>
                <p><strong>Tax ID (TIN):</strong> {{:SupplierTIN}}</p>
                <p><strong>Registration No (BRN):</strong> {{:SupplierRegistrationNo}}</p>
                <p><strong>SST Registration:</strong> {{:SupplierSSTNo}}</p>
                <p><strong>Tourism Tax No:</strong> {{:SupplierTourismTaxNo}}</p>
                <p><strong>MSIC Code:</strong> {{:SupplierMSICCode}}</p>
                <p><strong>Business Activity:</strong> {{:SupplierBusinessActivity}}</p>
                <p><strong>Address:</strong> {{:SupplierAddress}}</p>
            </div>
            <div class="info-section">
                <h2>Invoice Details</h2>
                <p><strong>Invoice Number:</strong> {{:InvoiceNumber}}</p>
                <p><strong>Invoice Type:</strong> {{:InvoiceType}}</p>
                <p><strong>Invoice Version:</strong> {{:InvoiceVersion}}</p>
                <p><strong>Invoice Date:</strong> {{:InvoiceDate}}</p>
                <p><strong>Original Invoice Ref:</strong> {{:OriginalInvoiceRefNo}}</p>
                <p><strong>Currency:</strong> {{:InvoiceCurrency}}</p>
                <p><strong>Exchange Rate:</strong> {{:CurrencyExchangeRate}}</p>
            </div>
        </div>

        <div class="info-section">
            <h2>Bill To</h2>
            <p><strong>Company Name:</strong> {{:BuyerName}}</p>
            <p><strong>Tax ID (TIN):</strong> {{:BuyerTIN}}</p>
            <p><strong>Registration No (BRN):</strong> {{:BuyerRegistrationNo}}</p>
            <p><strong>SST Registration:</strong> {{:BuyerSSTNo}}</p>
            <p><strong>MSIC Code:</strong> {{:BuyerMSICCode}}</p>
            <p><strong>Address:</strong> {{:BuyerAddress}}</p>
            <p><strong>City:</strong> {{:BuyerCity}}</p>
            <p><strong>Postal Code:</strong> {{:BuyerPostalCode}}</p>
            <p><strong>Country Code:</strong> {{:BuyerCountryCode}}</p>
            <p><strong>Contact:</strong> {{:BuyerContactNo}}</p>
            <p><strong>Email:</strong> {{:BuyerEmail}}</p>
        </div>

        <table style="margin-top: 30px;">
            <thead>
                <tr>
                    <th>Classification</th>
                    <th>Description</th>
                    <th>Unit Price</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                {{for items}}
                <tr>
                    <td>{{:Classification}}</td>
                    <td>{{:Description}}</td>
                    <td class="text-right">{{:UnitPrice}}</td>
                    <td class="text-right">{{:Amount}}</td>
                </tr>
                {{/for}}
            </tbody>
        </table>

        <div class="info-section">
            <h2>Tax Information</h2>
            <p><strong>Tax Type:</strong> {{if TaxType === '01'}}Sales Tax{{else TaxType === '02'}}Service Tax{{else TaxType === '03'}}Tourism Tax{{else TaxType === '04'}}High-Value Goods Tax{{else TaxType === '05'}}Sales Tax on Low Value Goods{{else TaxType === '06'}}Not Applicable{{else TaxType === 'E'}}Tax Exemption{{/if}} ({{:TaxType}})</p>
            <p><strong>Tax Rate:</strong> {{:TaxRate}}</p>
            <p><strong>Tax Exemption Details:</strong> {{:TaxExemptionDetails}}</p>
            <p><strong>Amount Tax Exemption:</strong> {{:AmountTaxExemption}}</p>
        </div>

        <div class="amount-box">
            <div class="amount-row">
                <span><strong>Total (Excluding Tax):</strong></span>
                <span>{{:TotalExcludingTax}}</span>
            </div>
            <div class="amount-row">
                <span><strong>Tax Amount:</strong></span>
                <span>{{:TaxAmount}}</span>
            </div>
            <div class="amount-row">
                <span><strong>Total (Including Tax):</strong></span>
                <span>{{:TotalIncludingTax}}</span>
            </div>
            <div class="amount-row total-row">
                <span><strong>Total Payable Amount:</strong></span>
                <span>{{:TotalPayableAmount}}</span>
            </div>
        </div>

        <div class="footer">
            <div class="footer-text">
                <p><strong>Digital Signature:</strong> {{:DigitalSignature}}</p>
                <p><strong>Date and Time of Validation:</strong> {{:InvoiceDateReceive}}</p>
                <p>This is a computer-generated invoice. No signature is required.</p>
            </div>
            <img class="qr-code" src="{{:QRCode}}" alt="QR Code">
        </div>
    </div>
</body>
</html>