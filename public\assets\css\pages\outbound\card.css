/* Cards Container */
.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    padding: 0.5rem;
}

.welcome-datetime {
    text-align: right;
    padding-left: 2rem;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  
  .current-time {
    font-size: 2rem;
    font-weight: 600;
    color: #fff;
    letter-spacing: 0.5px;
    text-shadow: none;
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }
  
  .current-date {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    display: flex;
    align-items: center;
  }
  
  /* Icons in datetime */
  .current-time i,
  .current-date i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    opacity: 0.9;
  }
  
  /* Welcome content styles */
  .welcome-content h4 {
    color: #fff;
    margin-bottom: 0.5rem;
  }

  .welcome-content p {
    color: #fff;
    margin-bottom: 0.5rem;
  }

/* Card Base Styles */
.info-card {
    background: #fff;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border-top: 4px solid transparent;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Card Info Layout */
.card-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

/* Card Icon */
.card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

/* Count Info */
.count-info {
    flex: 1;
}

.count-info h6 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
}

.count-info .text-muted {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Card Title */
.card-title-new {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Card Variants */
.total-invoice-card {
    border-top-color: #3b82f6;
}

.total-invoice-card .card-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.total-submitted-card {
    border-top-color: #10b981;
}

.total-submitted-card .card-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.total-rejected-card {
    border-top-color: #ef4444;
}


.total-rejected-card .card-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.total-invalid-card {
    border-top-color: #ef4444;
}

.total-invalid-card .card-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.total-cancelled-card {
    border-top-color: #f59e0b;
}

.total-cancelled-card .card-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.total-pending-card {
    border-top-color: #6366f1;
}

.total-pending-card .card-icon {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    color: white;
}

/* Loading Spinner */
.loading-spinner {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 0.15em;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .cards-container {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .cards-container {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        padding: 0.25rem;
    }

    .info-card {
        padding: 1.25rem;
    }

    .card-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }

    .count-info h6 {
        font-size: 1.25rem;
    }
}

@media (max-width: 576px) {
    .cards-container {
        grid-template-columns: 1fr;
    }
}
