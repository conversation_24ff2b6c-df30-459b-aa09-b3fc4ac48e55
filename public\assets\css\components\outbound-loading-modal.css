.outbound-loading-modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.outbound-loading-modal .modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.outbound-loading-modal .modal-body {
  padding: 2rem;
}

.outbound-loading-modal .loading-content {
  text-align: center;
}

.outbound-loading-modal .loading-message {
  font-size: 1.1rem;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.outbound-loading-modal .loading-time-left {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1.5rem;
}

.outbound-loading-modal .progress {
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  margin-bottom: 2rem;
  overflow: hidden;
}

.outbound-loading-modal .progress-bar {
  background-color: #0d6efd;
  transition: width 0.3s ease;
}

.outbound-loading-modal .steps-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: left;
}

.outbound-loading-modal .step {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.outbound-loading-modal .step[data-status="waiting"] {
  opacity: 0.7;
}

.outbound-loading-modal .step[data-status="in-progress"] {
  background-color: #e7f1ff;
  border-left: 4px solid #0d6efd;
}

.outbound-loading-modal .step[data-status="completed"] {
  background-color: #e8f5e9;
  border-left: 4px solid #2e7d32;
}

.outbound-loading-modal .step-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.outbound-loading-modal .step[data-status="waiting"] .step-icon i {
  color: #6c757d;
}

.outbound-loading-modal .step[data-status="in-progress"] .step-icon i {
  color: #0d6efd;
}

.outbound-loading-modal .step[data-status="completed"] .step-icon i {
  color: #2e7d32;
}

.outbound-loading-modal .step-content {
  flex: 1;
}

.outbound-loading-modal .step-label {
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 0.25rem;
}

.outbound-loading-modal .step-status {
  font-size: 0.85rem;
  color: #666;
}

.outbound-loading-modal .step[data-status="in-progress"] .step-status {
  color: #0d6efd;
}

.outbound-loading-modal .step[data-status="completed"] .step-status {
  color: #2e7d32;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.outbound-loading-modal .spin {
  animation: spin 1s linear infinite;
}

.outbound-loading-modal .btn-ok {
  min-width: 100px;
  margin-top: 1.5rem;
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.outbound-loading-modal .btn-ok:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
} 