.selected-docs-list {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin-bottom: 0.75rem;
}

/* Button group styling */
.d-flex.align-items-center.gap-2 {
    display: flex;
    align-items: center;
    gap: 0.5rem !important;
}

.d-flex.align-items-center.gap-2 .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.d-flex.align-items-center.gap-2 .btn i {
    font-size: 1rem;
}

.form-select.form-select-sm {
    min-width: 70px;
    margin: 0 0.5rem;
}

.selected-docs-list .list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
}

.selected-docs-list .list-group-item i {
    margin-right: 0.5rem;
    color: #6c757d;
}

.results-list {
    max-height: 400px;
    overflow-y: auto;
    margin-top: 1rem;
}

.results-list .alert {
    margin-bottom: 0.5rem;
}

#submissionProgress {
    max-height: 500px;
    overflow-y: auto;
}

#submissionProgress .alert {
    margin-bottom: 0.5rem;
}

.submission-options {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.25rem;
    margin-top: 0.75rem;
}

.validation-status {
    min-height: 50px;
}

#consolidatedSubmitBtn:disabled {
    cursor: not-allowed;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

/* Progress indicators */
.progress-step {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    background-color: #f8f9fa;
}

.progress-step i {
    margin-right: 0.5rem;
}

.progress-step.success {
    background-color: #d4edda;
    color: #155724;
}

.progress-step.error {
    background-color: #f8d7da;
    color: #721c24;
}

.progress-step.pending {
    background-color: #fff3cd;
    color: #856404;
}


.modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.modal-header {
    padding: 0.75rem 1rem;
    background: #fff;
    border-bottom: 1px solid #eee;
}

.modal-header .modal-title {
    font-size: 1.1rem;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-header .modal-title i {
    color: #0d6efd;
}

.modal-header .btn-close {
    padding: 0.75rem;
    margin: -0.5rem -0.5rem -0.5rem auto;
}

.modal-body {
    padding: 1rem;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.selected-docs-container h6 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.selected-docs-list:empty {
    padding: 1rem;
    text-align: center;
    color: #6c757d;
}

.selected-docs-list:empty:before {
    content: "No documents selected";
    font-style: italic;
}

.selected-docs-list .doc-item {
    display: flex;
    align-items: center;
    padding: 0.35rem;
    border-bottom: 1px solid #e9ecef;
    gap: 0.5rem;
    font-size: 0.85rem;
}

.selected-docs-list .doc-item:last-child {
    border-bottom: none;
}

.selected-docs-list .doc-item .company-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background: #e7f0ff;
    color: #0d6efd;
}

.submission-options .form-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.submission-options .form-select {
    border-color: #dee2e6;
    font-size: 0.9rem;
}

.modal-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid #eee;
    gap: 0.5rem;
}

.modal-footer .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.modal-footer .btn-secondary {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #666;
}

.modal-footer .btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.modal-footer .btn-primary:disabled {
    background-color: #0d6efd;
    border-color: #0d6efd;
    opacity: 0.65;
}

/* Animation for modal */
.modal.fade .modal-dialog {
    transform: scale(0.95);
    transition: transform 0.2s ease-out;
}

.modal.show .modal-dialog {
    transform: scale(1);
}

/* User Guidance Styles */
.user-guidance {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    margin-bottom: 0.75rem;
}

.guidance-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.guidance-header i {
    color: #0d6efd;
    font-size: 1.25rem;
}

.guidance-header h6 {
    margin: 0;
    color: #333;
    font-weight: 500;
}

.guidance-steps {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.guidance-step {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.step-number {
    background: #0d6efd;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 500;
}

.step-text {
    color: #495057;
    font-size: 0.85rem;
    line-height: 1.2;
}

.guidance-note {
    margin-top: 0.75rem;
    padding: 0.5rem;
    background: #fff3cd;
    border: 1px solid #ffeeba;
    border-radius: 6px;
    color: #856404;
    font-size: 0.8rem;
}

.guidance-note i {
    color: #856404;
}

.guidance-requirements {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    margin-top: 0.75rem;
}

.requirements-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #0d6efd;
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.requirements-title i {
    font-size: 1.1rem;
}

.requirements-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.requirements-list li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.35rem 0;
    font-size: 0.8rem;
    color: #495057;
    border-bottom: 1px solid #f1f3f5;
}

.requirements-list li:last-child {
    border-bottom: none;
}

.requirements-list li i {
    color: #198754;
    font-size: 1rem;
} 