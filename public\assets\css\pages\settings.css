/* Settings Page Styles */
.settings-container {
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 200px);
    background: #f8fafc;
}

.settings-content {
    display: grid;
    grid-template-columns: 320px 1fr;
    gap: 1rem;
    position: relative;
}

/* Navigation */
.settings-nav-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    position: sticky;
    height: calc(100vh - 100px);
    overflow-y: auto;
}

.settings-nav-title {
    padding: 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.settings-nav-items {
    padding: 1rem;
}

.settings-nav-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    margin: 0.5rem 0;
    border-radius: 12px;
    color: #475569;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    background: white;
}

.settings-nav-item:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: #eff6ff;
    z-index: 0;
    transition: width 0.3s ease;
}

.settings-nav-item:hover:not(.disabled):before {
    width: 100%;
}

.settings-nav-item:hover:not(.disabled) {
    color: #3b82f6;
    transform: translateX(6px);
    background: #f8fafc;
}

.settings-nav-item.active {
    background: #eff6ff;
    color: #3b82f6;
    font-weight: 600;
}

.settings-nav-icon {
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: #64748b;
    font-size: 1.25rem;
    border-radius: 10px;
    background: white;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.settings-nav-item:hover:not(.disabled) .settings-nav-icon {
    color: #3b82f6;
    transform: scale(1.1) rotate(5deg);
    background: white;
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2);
}

.settings-nav-item.active .settings-nav-icon {
    color: #3b82f6;
    background: white;
    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2);
}

.settings-nav-details {
    position: relative;
    z-index: 2;
}

.settings-nav-details h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    transition: transform 0.3s ease;
    color: inherit;
}

.settings-nav-details p {
    margin: 0.25rem 0 0;
    font-size: 0.875rem;
    color: #64748b;
    transition: transform 0.3s ease;
}

.settings-nav-item:hover .settings-nav-details p {
    color: #3b82f6;
    opacity: 0.8;
}

/* Form Section */
.settings-form-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    height: calc(100vh - 100px);
    overflow-y: auto;
}

.settings-form {
    padding: 2rem;
    display: none;
}

.settings-form.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-form-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    padding-bottom: 1.25rem;
    border-bottom: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.settings-form-title i {
    color: #3b82f6;
}

/* Form Styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #475569;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control[readonly] {
    background-color: #f1f3f5;
    cursor: not-allowed;
    opacity: 0.8;
    border-color: #e2e8f0;

    user-select: none;
}

/* Form Actions */
.settings-form-actions {
    margin-top: 1rem;
    padding-top: 1.25rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-outline-danger {
    background: transparent;
    border: 1px solid #dc2626;
    color: #dc2626;
}

.btn-outline-danger:hover {
    background: #fee2e2;
}

/* Responsive */
@media (max-width: 768px) {
    .settings-content {
        grid-template-columns: 1fr;
    }
    
    .settings-nav-card {
        position: relative;
        top: 0;
    }
}

/* Help Page Styles */
.help-steps {
    display: grid;
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.help-step {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    transition: all 0.2s ease;
}

.help-step:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.step-number {
    background: #3b82f6;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content h4 {
    color: #1e293b;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.step-content h4 i {
    color: #3b82f6;
    font-size: 1.25rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Specific colors for different icons */
.step-content h4 i.fa-building {
    color: #8b5cf6; /* Purple */
}

.step-content h4 i.fa-sliders-h {
    color: #f59e0b; /* Amber */
}

.step-content h4 i.fa-users {
    color: #10b981; /* Emerald */
}

.step-content h4 i.fa-file-invoice {
    color: #3b82f6; /* Blue */
}

.step-content h4 i.fa-chart-bar {
    color: #6366f1; /* Indigo */
}

.step-content h4 i.fa-paper-plane {
    color: #ec4899; /* Pink */
}

.step-content h4 i.fa-inbox {
    color: #f59e0b; /* Amber */
}

.step-content h4 i.fa-cog {
    color: #6366f1; /* Indigo */
}

.step-content ul {
    list-style: none;
    padding-left: 0;
    margin: 0.75rem 0;
}

.step-content li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
}

.step-content li:before {
    content: "•";
    color: #3b82f6;
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* Contact Support Section */
.contact-support {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.contact-method {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: all 0.2s ease;
    min-height: 100px;
}

.contact-method:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.contact-method i {
    background: #3b82f6;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.contact-method-details {
    flex: 1;
    min-width: 0; /* Allows text truncation to work */
}

.contact-method-details h5 {
    color: #1e293b;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.contact-method-details p {
    color: #64748b;
    margin: 0;
    word-break: break-all;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* FAQ Section */
.faq-list {
    display: grid;
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.faq-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.faq-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.faq-item h4 {
    color: #1e293b;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
}

.faq-item p {
    color: #64748b;
    margin: 0;
    line-height: 1.6;
}

/* Video Tutorials */
.video-tutorials {
    display: grid;
    gap: 2rem;
    margin: 1.5rem 0;
}

.video-item {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.video-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.video-item h4 {
    color: #1e293b;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.video-item h4 i {
    color: #ef4444;
    font-size: 1.25rem;
}

.video-wrapper {
    position: relative;
    width: 100%;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
    margin-bottom: 1rem;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-wrapper video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.video-item p {
    color: #64748b;
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Responsive video grid */
@media (min-width: 768px) {
    .video-tutorials {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .video-tutorials {
        grid-template-columns: 1fr;
    }
}


.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 500;
    color: #344767;
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    border: 1px solid #e9ecef;
    padding: 0.625rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

.form-control[readonly] {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    border: 1px solid #e9ecef;
}

.input-group-text.bg-light {
    background-color: #f8f9fa;
    border-color: #e9ecef;
}

.input-group > .form-control:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group > .input-group-text:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn {
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #6366f1;
    border-color: #6366f1;
}

.btn-primary:hover {
    background-color: #4f46e5;
    border-color: #4f46e5;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #e9ecef;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.text-end {
    text-align: right !important;
}

.section-title {
    color: #344767;
    font-size: 1.125rem;
    font-weight: 600;
}

.text-muted {
    color: #6c757d !important;
    font-size: 0.875rem;
}

/* Specific section styles */
.security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.security-section {
    background: #fff;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.notification-settings,
.sap-settings,
.lhdn-settings {
    border-radius: 8px;
    padding: 1.5rem;
}