<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Horizontal Loading Animation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="public/assets/css/modern-modal.css" rel="stylesheet">
    <style>
        body {
            padding: 2rem;
            background: #f8fafc;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>JSON Loading Animation Test</h1>
        <p>Testing the horizontal loading animation for JSON preview.</p>
        
        <div class="test-section">
            <h3>Horizontal Loading Animation</h3>
            <div class="json-loading-animation">
                <div class="loading-steps horizontal-steps">
                    <div class="loading-step active">
                        <div class="step-icon">
                            <i class="bi bi-check-circle-fill"></i>
                        </div>
                        <div class="step-title">Validating</div>
                        <div class="step-status">COMPLETE</div>
                    </div>
                    <div class="loading-connector active"></div>
                    <div class="loading-step processing">
                        <div class="step-icon">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div class="step-title">Processing</div>
                        <div class="step-status">IN PROGRESS</div>
                    </div>
                    <div class="loading-connector"></div>
                    <div class="loading-step">
                        <div class="step-icon">
                            <i class="bi bi-circle"></i>
                        </div>
                        <div class="step-title">Ready</div>
                        <div class="step-status">Waiting</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Animation Controls</h3>
            <button id="startAnimation" class="btn btn-primary">Start Animation</button>
            <button id="resetAnimation" class="btn btn-secondary">Reset</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('startAnimation').addEventListener('click', function() {
            const steps = document.querySelectorAll('.loading-step');
            const connectors = document.querySelectorAll('.loading-connector');
            
            // Reset all steps
            steps.forEach((step, index) => {
                if (index === 0) return; // Keep first step active
                step.classList.remove('active', 'processing');
                const statusEl = step.querySelector('.step-status');
                const iconEl = step.querySelector('.step-icon');
                if (statusEl) statusEl.textContent = 'Waiting';
                if (iconEl) iconEl.innerHTML = '<i class="bi bi-circle"></i>';
            });
            
            // Reset connectors
            connectors.forEach(connector => {
                connector.classList.remove('active');
            });
            
            // Start animation sequence
            setTimeout(() => {
                if (steps[1]) {
                    steps[1].classList.add('processing');
                    const statusEl = steps[1].querySelector('.step-status');
                    const iconEl = steps[1].querySelector('.step-icon');
                    if (statusEl) statusEl.textContent = 'IN PROGRESS';
                    if (iconEl) iconEl.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>';
                }
                
                setTimeout(() => {
                    if (steps[1]) {
                        steps[1].classList.remove('processing');
                        steps[1].classList.add('active');
                        const statusEl = steps[1].querySelector('.step-status');
                        const iconEl = steps[1].querySelector('.step-icon');
                        if (statusEl) statusEl.textContent = 'COMPLETE';
                        if (iconEl) iconEl.innerHTML = '<i class="bi bi-check-circle-fill"></i>';
                    }
                    if (connectors[0]) connectors[0].classList.add('active');
                    
                    setTimeout(() => {
                        if (steps[2]) {
                            steps[2].classList.add('processing');
                            const statusEl = steps[2].querySelector('.step-status');
                            const iconEl = steps[2].querySelector('.step-icon');
                            if (statusEl) statusEl.textContent = 'IN PROGRESS';
                            if (iconEl) iconEl.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div>';
                        }
                        
                        setTimeout(() => {
                            if (steps[2]) {
                                steps[2].classList.remove('processing');
                                steps[2].classList.add('active');
                                const statusEl = steps[2].querySelector('.step-status');
                                const iconEl = steps[2].querySelector('.step-icon');
                                if (statusEl) statusEl.textContent = 'COMPLETE';
                                if (iconEl) iconEl.innerHTML = '<i class="bi bi-check-circle-fill"></i>';
                            }
                            if (connectors[1]) connectors[1].classList.add('active');
                        }, 1500);
                    }, 1000);
                }, 1000);
            }, 500);
        });
        
        document.getElementById('resetAnimation').addEventListener('click', function() {
            location.reload();
        });
    </script>
</body>
</html>
