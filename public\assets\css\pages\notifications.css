/* Notifications Page Styles */

.notification-stat-card {
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.notification-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.notification-filters {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #6c757d;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
}

.filter-btn.active {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

.notifications-list {
  max-height: 600px;
  overflow-y: auto;
}

.notification-item {
  padding: 16px 20px;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;
  cursor: pointer;
  position: relative;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
}

.notification-item.unread:hover {
  background-color: #ffeaa7;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
}

.notification-icon.system {
  background: #6c757d;
}

.notification-icon.lhdn {
  background: #17a2b8;
}

.notification-icon.announcement {
  background: #28a745;
}

.notification-icon.alert {
  background: #dc3545;
}

.notification-body {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  color: #212529;
  margin-bottom: 4px;
  font-size: 0.95rem;
  line-height: 1.3;
}

.notification-message {
  color: #6c757d;
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.75rem;
  color: #adb5bd;
}

.notification-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

.notification-type {
  display: flex;
  align-items: center;
  gap: 4px;
}

.notification-priority {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
}

.notification-priority.low {
  background: #e3f2fd;
  color: #1976d2;
}

.notification-priority.normal {
  background: #f3e5f5;
  color: #7b1fa2;
}

.notification-priority.high {
  background: #fff3e0;
  color: #f57c00;
}

.notification-priority.urgent {
  background: #ffebee;
  color: #d32f2f;
}

.notification-actions {
  position: absolute;
  top: 16px;
  right: 20px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.notification-action-btn {
  background: none;
  border: none;
  color: #6c757d;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-action-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.unread-indicator {
  position: absolute;
  top: 20px;
  left: 8px;
  width: 8px;
  height: 8px;
  background: #007bff;
  border-radius: 50%;
}

/* Modal Styles */
.notification-modal-content {
  padding: 20px 0;
}

.notification-modal-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #dee2e6;
}

.notification-modal-body {
  line-height: 1.6;
  color: #495057;
}

.notification-modal-meta {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  margin-top: 16px;
}

.notification-modal-meta .row {
  margin: 0;
}

.notification-modal-meta .col-sm-3 {
  font-weight: 600;
  color: #6c757d;
  padding: 4px 0;
}

.notification-modal-meta .col-sm-9 {
  padding: 4px 0;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-notification {
  padding: 16px 20px;
  border-bottom: 1px solid #f1f3f4;
}

.skeleton-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
}

.skeleton-line {
  height: 12px;
  background: #e9ecef;
  border-radius: 6px;
  margin-bottom: 8px;
}

.skeleton-line.title {
  width: 70%;
}

.skeleton-line.message {
  width: 90%;
}

.skeleton-line.meta {
  width: 40%;
  height: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-filters {
    justify-content: center;
  }
  
  .filter-btn {
    font-size: 0.8rem;
    padding: 6px 12px;
  }
  
  .notification-item {
    padding: 12px 16px;
  }
  
  .notification-content {
    gap: 8px;
  }
  
  .notification-icon {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }
  
  .notification-actions {
    position: static;
    opacity: 1;
    margin-top: 8px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .notification-item {
    border-bottom-color: #343a40;
  }
  
  .notification-item:hover {
    background-color: #343a40;
  }
  
  .notification-item.unread {
    background-color: #495057;
    border-left-color: #ffc107;
  }
  
  .notification-title {
    color: #f8f9fa;
  }
  
  .notification-message {
    color: #adb5bd;
  }
}
