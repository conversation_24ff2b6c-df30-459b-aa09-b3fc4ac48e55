/* Modern Error Modal Styles - Consistent Design with Fixed Layout */
.modern-error-modal {
    background: white;
    border-radius: 16px;
    padding: 0;
    max-width: 600px;
    margin: 2rem auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    position: relative;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    display: flex;
    flex-direction: column;
}

/* Error Header - Red Theme for Error States */
.error-header {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
    border-radius: 16px 16px 0 0;
    margin: 0;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
}

.error-icon {
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.error-icon .icon-wrapper {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: #ef4444;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

.error-icon .icon-wrapper i {
    font-size: 1.5rem;
    color: #ef4444;
}

.error-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: white;
    position: relative;
    z-index: 2;
}

.error-subtitle {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 1.5rem 0;
    position: relative;
    z-index: 2;
}

/* Error Meta - Same as Submission Modal */
.error-meta {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    position: relative;
    z-index: 2;
}

.error-meta .meta-item {
    text-align: center;
}

.error-meta .meta-label {
    display: block;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.error-meta .meta-value {
    display: block;
    font-size: 1rem;
    color: white;
    font-weight: 600;
}

/* Error Content - Consistent Design with Proper Margins */
.error-content {
    padding: 2rem;
    background: white;
    margin: 0;
    border-radius: 0 0 16px 16px;
}

.error-code-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.75rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.error-message {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border: 2px solid #fecaca;
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
}

.error-message h6 {
    color: #dc2626;
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.error-message p {
    color: #7f1d1d;
    margin: 0;
    line-height: 1.6;
}

.error-details {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.error-details h6 {
    color: #475569;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-details ul {
    margin: 0;
    padding-left: 1.5rem;
}

.error-details li {
    color: #64748b;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.error-suggestion {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 1px solid #f59e0b;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.error-suggestion h6 {
    color: #92400e;
    font-weight: 700;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-suggestion p {
    color: #78350f;
    margin: 0;
    line-height: 1.6;
}

.error-information {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border: 1px solid #3b82f6;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.error-information h6 {
    color: #1d4ed8;
    font-weight: 700;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-information p {
    color: #1e40af;
    margin: 0;
    line-height: 1.6;
}

/* Error Actions - Consistent Design with Proper Margins */
.error-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 1px solid rgba(226, 232, 240, 0.6);
    margin: 0;
    border-radius: 0 0 16px 16px;
}

.error-btn {
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    cursor: pointer;
    min-width: 140px;
    justify-content: center;
}

.error-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.error-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.error-btn-secondary {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #475569;
    box-shadow: 0 4px 12px rgba(71, 85, 105, 0.1);
}

.error-btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(71, 85, 105, 0.15);
}

/* Simple Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Prevent Footer Conflicts */
.modern-error-modal .error-actions:last-child {
    margin-bottom: 0;
}

.modern-error-modal .error-content + .error-actions {
    border-top: 1px solid rgba(226, 232, 240, 0.6);
}

/* Ensure proper modal positioning */
.swal2-popup.modern-error-popup {
    margin: 2rem auto !important;
    padding: 0 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-error-modal {
        max-width: 95%;
        margin: 1rem auto;
    }

    .error-header {
        padding: 1.5rem;
    }

    .error-icon .icon-wrapper {
        width: 56px;
        height: 56px;
    }

    .error-icon .icon-wrapper i {
        font-size: 1.25rem;
    }

    .error-title {
        font-size: 1.125rem;
    }

    .error-subtitle {
        font-size: 0.8rem;
    }

    .error-meta {
        gap: 1rem;
    }

    .error-content {
        padding: 1.5rem;
    }

    .error-actions {
        flex-direction: column;
        gap: 0.75rem;
        padding: 1.5rem;
    }

    .error-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .error-header {
        padding: 1.25rem;
    }

    .error-icon .icon-wrapper {
        width: 48px;
        height: 48px;
    }

    .error-icon .icon-wrapper i {
        font-size: 1.125rem;
    }

    .error-title {
        font-size: 1rem;
    }

    .error-content {
        padding: 1.25rem;
    }

    .error-actions {
        padding: 1.25rem;
    }

    .error-btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.8rem;
    }
}
