<!-- ======= Header ======= -->
<link rel="stylesheet" href="/assets/css/header.css">
<link rel="stylesheet" href="/assets/css/notification-dropdown.css">
<div id="webcrumbs">
  <header class="bg-white shadow-lg border-b">
    <div class="px-2 py-1">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-8">
          <div class="pinnacle-header__logo-section">
            <a
              href="/dashboard"
              class="transform hover:scale-105 transition-transform duration-200"
              style="display: flex; align-items: center; text-decoration: none;"
            >
              <img
                src="/assets/img/logo4.png"
                alt="Logo"
                class="pinnacle-header__logo"
              />
            </a>
          </div>
          <nav class="flex gap-6">
            <a
              href="/dashboard"
              class="flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-blue-50 transition-all duration-200 group"
            >
              <span
                class="material-symbols-outlined text-blue-600 group-hover:scale-110 transition-transform"
                >dashboard</span
              >
              <span>Dashboard</span>
            </a>
            <a
              href="/outbound"
              class="flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-blue-50 transition-all duration-200 group"
            >
              <span
                class="material-symbols-outlined text-blue-600 group-hover:scale-110 transition-transform"
                >send</span
              >
              <span>Outbound</span>
            </a>
            <a
              href="/inbound"
              class="flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-blue-50 transition-all duration-200 group"
            >
              <span
                class="material-symbols-outlined text-blue-600 group-hover:scale-110 transition-transform"
                >download</span
              >
              <span>Inbound</span>
            </a>
          <a
            href="/consolidated"
            class="flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-blue-50 transition-all duration-200 group"
          >
            <span
              class="material-symbols-outlined text-blue-600 group-hover:scale-110 transition-transform"
              >unfold_more_double</span
            >
            <span>Consolidated</span>
          </a>

          </nav>
        </div>
        <div class="flex items-center gap-4">
          <!-- Notifications Dropdown -->
          <div class="relative notification-dropdown-container">
            <button
              id="notificationDropdownBtn"
              class="relative p-3 rounded-lg hover:bg-slate-50 transition-all duration-200 group"
            >
              <span class="material-symbols-outlined text-slate-600 group-hover:scale-110 transition-transform text-xl">
                notifications
              </span>
              <span
                id="notificationBadge"
                class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-semibold"
                style="display: none;"
              >0</span>
            </button>

            <!-- Notification Dropdown -->
            <div
              id="notificationDropdown"
              class="notification-dropdown absolute right-0 top-full mt-2 w-96 bg-white rounded-xl shadow-xl border border-gray-200 z-50 hidden"
            >
              <div class="p-4 border-b border-gray-100 bg-gray-50">
                <div class="flex items-center justify-between">
                  <h4 class="font-semibold text-gray-800 text-lg">Notifications</h4>
                  <button
                    class="text-sm text-blue-600 hover:text-blue-800 font-medium px-2 py-1 rounded hover:bg-blue-50 transition-colors"
                    onclick="markAllNotificationsRead()"
                  >
                    Mark all read
                  </button>
                </div>
              </div>

              <!-- Notification List -->
              <div id="notificationDropdownList" class="max-h-80 overflow-y-auto">
                <!-- Loading state -->
                <div id="notificationDropdownLoading" class="p-6 text-center">
                  <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p class="text-sm text-slate-600 mt-3">Loading notifications...</p>
                </div>

                <!-- Empty state -->
                <div id="notificationDropdownEmpty" class="p-8 text-center hidden">
                  <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <span class="material-symbols-outlined text-gray-400 text-2xl">notifications_off</span>
                  </div>
                  <h5 class="text-gray-700 font-medium mb-2">There are no new notifications</h5>
                  <p class="text-gray-500 text-sm mb-4">When you get new notifications, they will be here</p>
                  <div class="flex justify-center gap-4">
                    <a href="/dashboard/notifications" class="text-blue-600 hover:text-blue-800 text-sm font-medium">All Notifications</a>
                    <a href="/dashboard/developer-settings" class="text-blue-600 hover:text-blue-800 text-sm font-medium">Settings</a>
                  </div>
                </div>

                <!-- Notifications will be loaded here -->
              </div>

              <div class="p-3 border-t border-gray-100 bg-gray-50">
                <a
                  href="/dashboard/notifications"
                  class="block text-center text-sm text-blue-600 hover:text-blue-800 font-medium py-2 px-4 rounded hover:bg-blue-50 transition-colors"
                >
                  View all notifications
                </a>
              </div>
            </div>
          </div>

          <!-- Profile Dropdown -->
          <div class="relative">
          <details class="group">
            <summary
              class="flex items-center gap-4 cursor-pointer list-none"
            >
              <div
                class="flex items-center gap-3 px-4 py-2 rounded-lg hover:bg-slate-50 transition-all duration-200"
              >
                <img
                  src="https://webcrumbs.cloud/placeholder"
                  alt="Profile"
                  class="w-10 h-10 rounded-full ring-2 ring-blue-100 profile-logo"
                  loading="lazy"
                />
                <div class="text-left">
                  <div class="flex items-center gap-2">
                    <span class="font-medium profile-username">Loading...</span>
                    <span
                      class="px-2 py-0.5 text-xs bg-red-600 text-white font-100 "
                      >Admin</span
                    >
                  </div>
                </div>
                <span
                  class="material-symbols-outlined group-open:rotate-180 transition-transform duration-200"
                  >expand_more</span
                >
              </div>
            </summary>
            <div
              class="absolute right-0 top-full mt-2 w-64 bg-white rounded-xl shadow-lg border p-2 z-50"
            >
              <div class="p-3 border-b">
                <h4 class="font-medium">Account Settings</h4>
                <p class="text-sm text-slate-600">
                  Manage your account preferences
                </p>
              </div>
              <div class="py-1">
                <a
                  href="/profile"
                  class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-50 transition-all duration-200 group"
                >
                  <span
                    class="material-symbols-outlined text-slate-600 group-hover:scale-110 transition-transform"
                    >business</span
                  >
                  <span>Company Profile</span>
                </a>
                <a
                  href="/users"
                  class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-50 transition-all duration-200 group"
                >
                  <span
                    class="material-symbols-outlined text-slate-600 group-hover:scale-110 transition-transform"
                    >group</span
                  >
                  <span>User Management</span>
                </a>
                <a
                  href="/dashboard/developer-settings"
                  class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-50 transition-all duration-200 group admin-only"
                  style="display: none;"
                >
                  <span
                    class="material-symbols-outlined text-slate-600 group-hover:scale-110 transition-transform"
                    >code</span
                  >
                  <span>Developer Settings</span>
                  <span
                    class="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full"
                    >New</span
                  >
                </a>
                <div class="h-px bg-slate-100 my-2"></div>
                <!-- <a
                  href="/changelog"
                  class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-50 transition-all duration-200 group"
                >
                  <span
                    class="material-symbols-outlined text-slate-600 group-hover:scale-110 transition-transform"
                    >history</span
                  >
                  <span class="flex-1">Changelog</span>
                  <span
                    class="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full"
                    >New</span
                  >
                </a> -->
                <a
                href="/dashboard/sdk-updates"
                class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-50 transition-all duration-200 group"
              >
                <span
                  class="material-symbols-outlined text-slate-600 group-hover:scale-110 transition-transform"
                  >rss_feed</span
                >
                <span class="flex-1">SDK Updates</span>
                <span
                  class="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full"
                  >New</span
                >
              </a>
                <a
                href="/help"
                class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-50 transition-all duration-200 group"
              >
                <span
                  class="material-symbols-outlined text-slate-600 group-hover:scale-110 transition-transform"
                  >help</span
                >
                <span class="flex-1">Help & Support</span>
                <span
                  class="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full"
                  >New</span
                >
              </a>

              <a
              href="/settings/user/admin/profile/{{ user.id }}"
              class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-50 transition-all duration-200 group"
            >
              <span
                class="material-symbols-outlined text-slate-600 group-hover:scale-110 transition-transform"
                >settings</span
              >
              <span>Settings</span>
            </a>
                <div class="h-px bg-slate-100 my-2"></div>
                <a
                  href="/auth/logout"
                  class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-red-50 text-red-600 transition-all duration-200 group"
                >
                  <span
                    class="material-symbols-outlined group-hover:scale-110 transition-transform"
                    >logout</span
                  >
                  <span>Sign Out</span>
                </a>
              </div>
            </div>
          </details>
        </div>
      </div>
    </div>
  </header>
  </div>