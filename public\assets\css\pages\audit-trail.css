/* Audit Container */
.audit-container {
    padding: 2rem;
    background-color: #f8fafc;
    min-height: calc(100vh - 60px);
}

.header-left {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.header-icon {
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.9);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.header-content h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: #fff;
}

.header-content p {
    margin: 0.5rem 0 0;
    opacity: 0.8;
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
}

.header-info {
    text-align: right;
    font-size: 0.875rem;
}

.header-info .time,
.header-info .date {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.header-info .time {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.header-info i {
    opacity: 0.8;
    font-size: 1rem;
}

/* Statistics Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: #fff;
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: #fff;
}

.stats-card.outbound .stats-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stats-card.inbound .stats-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stats-card.active .stats-icon {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.stats-info {
    flex: 1;
}

.stats-label {
    display: block;
    color: #64748b;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.stats-info h3 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

.stats-type {
    display: block;
    color: #64748b;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Filters Section */
.filters-section {
    background: #fff;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    color: #1e293b;
    background-color: #fff;
}

.form-control:focus,
.form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.input-group-text {
    background-color: #f8fafc;
    border-color: #e2e8f0;
    color: #64748b;
    font-size: 0.875rem;
}

/* Content Card */
.content-card {
    background: #fff;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: none;
}

.content-card .card-header {
    padding: 1.25rem 1.5rem;
    background: #fff;
    border-bottom: 1px solid #e2e8f0;
    border-radius: 0.75rem 0.75rem 0 0;
}

.content-card .card-header h5 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
}

/* Table Styles */
.table {
    margin: 0;
}

.table th {
    background: #f8fafc;
    font-weight: 600;
    color: #1e293b;
    font-size: 0.875rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.table td {
    padding: 1rem 1.5rem;
    color: #475569;
    font-size: 0.875rem;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8fafc;
}

/* Badges */
.badge {
    padding: 0.35rem 0.65rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
}

.badge i {
    margin-right: 0.25rem;
}

/* Status Colors */
.bg-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%) !important;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    color: #fff;
    font-weight: 500;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

.btn-outline-primary {
    color: #3b82f6;
    border: 1px solid #3b82f6;
    font-weight: 500;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-color: transparent;
    color: #fff;
}

.btn-outline-secondary {
    color: #64748b;
    border: 1px solid #e2e8f0;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.5rem;
}

.btn-outline-secondary:hover:not(:disabled) {
    background-color: #f8fafc;
    border-color: #cbd5e1;
    color: #475569;
}

/* Card Footer */
.card-footer {
    padding: 1rem 1.5rem;
    background: #fff;
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 0.75rem 0.75rem;
}

.pagination-info {
    color: #64748b;
    font-size: 0.875rem;
}

/* Loading States */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 0.15em;
    color: #3b82f6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .audit-container {
        padding: 1rem;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem;
    }

    .header-info {
        width: 100%;
        text-align: left;
    }

    .header-info .time,
    .header-info .date {
        justify-content: flex-start;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .filters-section {
        padding: 1rem;
    }

    .table th,
    .table td {
        padding: 0.75rem 1rem;
    }

    .card-footer {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }
} 