
/* Style for the custom confirmation popup */
.confirmation-popup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.confirmation-box {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 400px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.popup-header h4 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.close-popup {
    font-size: 24px;
    cursor: pointer;
    color: #888;
}

.popup-body {
    margin-bottom: 20px;
    font-size: 16px;
    color: #555;
}

.popup-footer {
    display: flex;
    justify-content: space-around;
}

.btn-cancel,
.btn-confirm {
    padding: 10px 20px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 14px;
}

.btn-cancel {
    background-color: #ddd;
    color: #333;
}

.btn-confirm {
    background-color: #007bff;
    color: white;
}

.btn-cancel:hover {
    background-color: #ccc;
}

.btn-confirm:hover {
    background-color: #0056b3;
}


.pdf-viewer-wrapper {
    position: relative;
    min-height: 600px;
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
}

#pdf-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#pdf-container {
    position: relative;
    z-index: 1;
    min-height: 600px;
}

#pdf-container iframe {
    display: block;
    width: 100%;
    height: 600px;
    border: none;
}

/* LHDN Inbound Modal Styles */
.lhdn-inbound-modal .modal-dialog {
    max-width: 1400px;
    width: 95%;
    margin: 1.75rem auto;
    height: calc(100vh - 3.5rem);
}

.lhdn-inbound-modal .modal-content {
    border: none;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(64, 81, 137, 0.15);
    width: 100%;
    background: #f8f9fa;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.lhdn-inbound-modal .modal-header {
    background: linear-gradient(135deg, #405189 0%, #3a4a7e 100%);
    border-bottom: none;
    border-radius: 16px 16px 0 0;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.lhdn-inbound-modal .modal-title {
    color: #ffffff;
    font-weight: 600;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.lhdn-inbound-modal #modal-invoice-number {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    margin-left: 0.5rem;
    font-weight: 500;
}

.lhdn-inbound-modal .modal-title i {
    font-size: 1.5rem;
    color: #ffffff;
}

.lhdn-inbound-modal .btn-close {
    color: #ffffff;
    opacity: 0.8;
    transition: opacity 0.2s;
    filter: invert(1) grayscale(100%) brightness(200%);
}

.lhdn-inbound-modal .btn-close:hover {
    opacity: 1;
}

/* Custom Dropdown Styles */
.lhdn-inbound-modal .custom-dropdown {
    position: relative;
    display: inline-block;
}

.lhdn-inbound-modal .dropdown-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.lhdn-inbound-modal .dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.25);
}

.lhdn-inbound-modal .dropdown-toggle i {
    font-size: 1rem;
    transition: transform 0.2s ease;
}

.lhdn-inbound-modal .dropdown-toggle.show i {
    transform: rotate(180deg);
}

.lhdn-inbound-modal .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: #ffffff;
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(64, 81, 137, 0.15);
    padding: 0.5rem;
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.lhdn-inbound-modal .dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lhdn-inbound-modal .dropdown-item {
    padding: 0.75rem 1rem;
    color: #1f2937;
    font-size: 0.9rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.2s ease;
}

.lhdn-inbound-modal .dropdown-item i {
    font-size: 1.1rem;
    color: #405189;
}

.lhdn-inbound-modal .dropdown-item:hover {
    background: rgba(64, 81, 137, 0.05);
    color: #405189;
}

.lhdn-inbound-modal .dropdown-divider {
    margin: 0.5rem 0;
    border-top: 1px solid rgba(64, 81, 137, 0.1);
}

.lhdn-inbound-modal .modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
    display: flex;
    gap: 1.5rem;
}

/* Custom scrollbar styling */
.lhdn-inbound-modal .modal-body::-webkit-scrollbar,
.lhdn-inbound-modal .info-sidebar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.lhdn-inbound-modal .modal-body::-webkit-scrollbar-track,
.lhdn-inbound-modal .info-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.lhdn-inbound-modal .modal-body::-webkit-scrollbar-thumb,
.lhdn-inbound-modal .info-sidebar::-webkit-scrollbar-thumb {
    background: #405189;
    border-radius: 10px;
    transition: all 0.2s ease;
}

.lhdn-inbound-modal .modal-body::-webkit-scrollbar-thumb:hover,
.lhdn-inbound-modal .info-sidebar::-webkit-scrollbar-thumb:hover {
    background: #364574;
}

/* Firefox scrollbar */
.lhdn-inbound-modal .modal-body,
.lhdn-inbound-modal .info-sidebar {
    scrollbar-width: thin;
    scrollbar-color: #405189 transparent;
}

/* Info Sections - Left Side */
.lhdn-inbound-modal .info-sidebar {
    width: 400px;
    min-width: 400px;
    max-width: 400px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(64, 81, 137, 0.08);
    overflow-y: auto;
    padding-right: 4px; /* Add padding to prevent content from touching scrollbar */
}

/* Info Section Styling */
.lhdn-inbound-modal .info-section {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(64, 81, 137, 0.1);
    position: relative;
}

.lhdn-inbound-modal .info-section:last-child {
    border-bottom: none;
}

.lhdn-inbound-modal .section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(64, 81, 137, 0.1);
}

.lhdn-inbound-modal .section-header i {
    font-size: 1.25rem;
    color: #405189;
}

.lhdn-inbound-modal .section-header span {
    color: #405189;
    font-weight: 600;
    font-size: 1.1rem;
}

.lhdn-inbound-modal .info-row {
    background-color: #f1f5f9;
    border: 1px solid rgba(64, 81, 137, 0.08);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.lhdn-inbound-modal .info-row:hover {
    border-color: rgba(64, 81, 137, 0.2);

}

.lhdn-inbound-modal .info-row:last-child {
    margin-bottom: 0;
}

.lhdn-inbound-modal .label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.lhdn-inbound-modal .value {
    color: #1f2937;
    font-weight: 500;
    word-break: break-word;
}

.lhdn-inbound-modal .highlight-row {
    background: rgba(64, 81, 137, 0.05);
    border: 1px solid rgba(64, 81, 137, 0.15);
    box-shadow: 0 2px 6px rgba(64, 81, 137, 0.1);
}

.lhdn-inbound-modal .highlight-row .value {
    color: #405189;
    font-size: 1.25rem;
    font-weight: 600;
}

/* PDF Section - Right Side */
.lhdn-inbound-modal .pdf-section {
    flex: 1;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(64, 81, 137, 0.08);
    overflow: hidden;
    margin-right: 4px; /* Add margin to prevent content from touching scrollbar */
}

.lhdn-inbound-modal .pdf-viewer-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: #f8f9fa;
    overflow: auto;
}

.lhdn-inbound-modal #pdfViewer {
    width: 100%;
    height: 100%;
    border: none;
}

.lhdn-inbound-modal .pdf-viewer-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.lhdn-inbound-modal .pdf-viewer-container::-webkit-scrollbar-track {
    background: transparent;
}

.lhdn-inbound-modal .pdf-viewer-container::-webkit-scrollbar-thumb {
    background: #405189;
    border-radius: 10px;
    transition: all 0.2s ease;
}

.lhdn-inbound-modal .pdf-viewer-container::-webkit-scrollbar-thumb:hover {
    background: #364574;
}

/* Loading State */
.lhdn-inbound-modal .loading-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.lhdn-inbound-modal .loading-container .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #405189;
}

.lhdn-inbound-modal .loading-text {
    margin-top: 1rem;
    color: #405189;
    font-weight: 500;
}

/* Status Badge Styles */
.lhdn-inbound-modal .badge-status {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
    font-size: 0.875rem;
    border: 1px solid transparent;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.lhdn-inbound-modal .badge-status i {
    font-size: 1rem;
}

.lhdn-inbound-modal .badge-status.Valid {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border-color: rgba(16, 185, 129, 0.2);
}

.lhdn-inbound-modal .badge-status.Cancelled {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.2);
}

.lhdn-inbound-modal .badge-status.Invalid {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.2);
}

.lhdn-inbound-modal .badge-status.Pending {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
    border-color: rgba(245, 158, 11, 0.2);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .lhdn-inbound-modal .modal-dialog {
        margin: 0;
        width: 100%;
        height: 100vh;
    }

    .lhdn-inbound-modal .modal-content {
        border-radius: 0;
        height: 100vh;
    }

    .lhdn-inbound-modal .modal-body {
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
    }

    .lhdn-inbound-modal .info-sidebar {
        width: 100%;
        min-width: 100%;
        max-width: 100%;
    }

    .lhdn-inbound-modal .pdf-section {
        height: 50vh;
    }
}

/* Print Styles */
@media print {
    .lhdn-inbound-modal .modal-content {
        border: none;
        box-shadow: none;
    }

    .lhdn-inbound-modal .info-sidebar {
        width: 100%;
        max-width: none;
        border: none;
    }

    .lhdn-inbound-modal .pdf-section {
        page-break-before: always;
    }
}