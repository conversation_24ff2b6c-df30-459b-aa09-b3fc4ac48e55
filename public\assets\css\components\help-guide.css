.help-guide-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease;
}

.help-guide-content {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    position: relative;
    z-index: 10000;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: slideUp 0.3s ease;
}

.help-guide-content h3 {
    color: #1e293b;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.help-guide-content p {
    color: #64748b;
    margin-bottom: 1.5rem;
}

.help-guide-content ul {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
    text-align: left;
}

.help-guide-content ul li {
    color: #475569;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.help-guide-content ul li i {
    color: #10b981;
}

.help-guide-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.help-guide-actions > div {
    display: flex;
    gap: 1rem;
}

.btn-view-help {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-view-help:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.btn-close-help {
    background: #f1f5f9;
    color: #64748b;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-close-help:hover {
    background: #e2e8f0;
}

.dont-show-today {
    font-size: 0.875rem;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    margin-top: 0.5rem;
}

.dont-show-today input {
    cursor: pointer;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
} 