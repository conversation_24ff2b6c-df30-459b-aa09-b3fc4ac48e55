/* Table Styles */
.table-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin-top: 1rem;
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.checkbox-column {
    width: 40px;
    text-align: center;
}

.uuid-column {
    min-width: 200px;
}

.form-check-input {
    cursor: pointer;
}

/* Status Badge Styles */
.badge {
    padding: 0.5em 0.8em;
    font-weight: 500;
}

/* Action Button Styles */
.action-dropdown-btn {
    padding: 0.25rem 0.5rem;
    background: transparent;
    border: none;
}

.action-dropdown-btn:hover {
    background: rgba(0,0,0,0.05);
}

.dropdown-menu {
    min-width: 200px;
    padding: 0.5rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.dropdown-item {
    padding: 0.5rem 1rem;
}

/* Search Box Styles */
.search-wrapper {
    position: relative;
}

.dataTables_filter input {
    border-radius: 4px;
    border: 1px solid #dee2e6;
    padding: 0.375rem 0.75rem;
    padding-right: 2.5rem;
    width: 100%;
}

.dataTables_filter input:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13,110,253,.25);
    outline: 0;
}

/* Loading Overlay */
.table-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
} 