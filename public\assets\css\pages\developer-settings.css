/* Developer Settings Page Styles */

.settings-nav-card {
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 20px;
}

.settings-nav-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-nav-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.settings-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  text-decoration: none;
  color: #6c757d;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.settings-nav-item:hover {
  background-color: #f8f9fa;
  color: #495057;
  text-decoration: none;
}

.settings-nav-item.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.settings-nav-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e9ecef;
  color: #6c757d;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.settings-nav-item.active .settings-nav-icon {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.settings-nav-details h6 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.settings-nav-details p {
  margin: 0;
  font-size: 0.75rem;
  opacity: 0.8;
}

.settings-section {
  display: none;
}

.settings-section.active {
  display: block;
}

/* Stats Cards */
.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.stat-content h4 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #212529;
}

.stat-content p {
  margin: 0;
  font-size: 0.875rem;
  color: #6c757d;
}

/* Filter Buttons */
.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-buttons .btn {
  border-radius: 20px;
  font-size: 0.875rem;
  padding: 6px 16px;
}

.filter-buttons .btn.active {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

/* Announcements Table */
.table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.875rem;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.published {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.draft {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.archived {
  background-color: #f8d7da;
  color: #721c24;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
}

.type-badge.general {
  background-color: #e3f2fd;
  color: #1976d2;
}

.type-badge.maintenance {
  background-color: #fff3e0;
  color: #f57c00;
}

.type-badge.feature {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.type-badge.security {
  background-color: #ffebee;
  color: #d32f2f;
}

.target-badge {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  background-color: #f1f3f4;
  color: #5f6368;
}

.category-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
}

.category-badge.feature {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.category-badge.update {
  background-color: #e3f2fd;
  color: #1976d2;
}

.category-badge.improvement {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.category-badge.bugfix {
  background-color: #fff3e0;
  color: #f57c00;
}

.category-badge.maintenance {
  background-color: #fff3e0;
  color: #f57c00;
}

.category-badge.security {
  background-color: #ffebee;
  color: #d32f2f;
}

/* Action Buttons */
.action-btn {
  background: none;
  border: none;
  color: #6c757d;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 2px;
}

.action-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.action-btn.edit:hover {
  color: #007bff;
}

.action-btn.delete:hover {
  color: #dc3545;
}

.action-btn.publish:hover {
  color: #28a745;
}

/* Modal Styles */
.modal-lg {
  max-width: 900px;
}

.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.modal-title {
  color: #495057;
  font-weight: 600;
}

/* Form Styles */
.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 6px;
}

.form-control:focus,
.form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check-label {
  font-size: 0.875rem;
  color: #6c757d;
}

/* Help System Styles */
.help-url-display {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

/* Portal Settings Styles */
.setting-group {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.setting-group h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 16px;
}

/* System Monitoring Styles */
.monitoring-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.monitoring-card h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 12px;
}

.health-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.875rem;
}

.health-indicator.good {
  background-color: #d4edda;
  color: #155724;
}

.health-indicator.warning {
  background-color: #fff3cd;
  color: #856404;
}

.health-indicator.error {
  background-color: #f8d7da;
  color: #721c24;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings-nav-card {
    position: static;
    margin-bottom: 20px;
  }

  .settings-nav-items {
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: 10px;
  }

  .settings-nav-item {
    flex-shrink: 0;
    min-width: 120px;
  }

  .settings-nav-details h6 {
    font-size: 0.8rem;
  }

  .settings-nav-details p {
    display: none;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-content h4 {
    font-size: 1.5rem;
  }

  .filter-buttons {
    justify-content: center;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .action-btn {
    padding: 2px 6px;
    font-size: 0.8rem;
  }
}
